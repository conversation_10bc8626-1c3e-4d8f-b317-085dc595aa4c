/* eslint-disable @typescript-eslint/no-require-imports */

import {
  //Globals
  backgroundBlur,
  borderRadius,
  borderWidth,
  //Brands
  brandSolution,
  fontSize,
  lineHeight,
  opacityLevelHex,
  shadowLevel,
  spacing,
} from '@ads/tokens'
import type { PluginAPI } from 'tailwindcss/types/config'
import { OptionalConfig } from 'tailwindcss/types/config'
import { createThemes } from 'tw-colors'

type ConfigWithoutContent = Partial<OptionalConfig>

const configShare: ConfigWithoutContent = {
  darkMode: ['class'],
  content: ['./src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        base: {
          ...brandSolution.baseColor,
        },
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        chart: {
          '1': 'hsl(var(--chart-1))',
          '2': 'hsl(var(--chart-2))',
          '3': 'hsl(var(--chart-3))',
          '4': 'hsl(var(--chart-4))',
          '5': 'hsl(var(--chart-5))',
        },
        sidebar: {
          DEFAULT: '#FFFFFF',
          foreground: 'hsl(var(--sidebar-foreground))',
          primary: 'hsl(var(--sidebar-primary))',
          'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
          accent: 'hsl(var(--sidebar-accent))',
          'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
          border: '#FFFFFF',
          ring: 'hsl(var(--sidebar-ring))',
        },
      },
      blur: {
        ...backgroundBlur,
      },
      borderRadius: {
        ...borderRadius,
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      borderWidth: {
        ...borderWidth,
      },
      fontFamily: {
        ...brandSolution.fontFamily,
        body: ['Inter', 'sans-serif'],
      },
      fontWeight: {
        ...brandSolution.fontWeight,
      },
      fontSize: {
        ...fontSize,
      },
      lineHeight: {
        ...lineHeight,
      },
      opacity: {
        ...opacityLevelHex,
      },
      boxShadow: {
        ...shadowLevel,
      },
      spacing: {
        ...spacing,
      },
    },
  },
  plugins: [
    function ({ addUtilities }: { addUtilities: PluginAPI['addUtilities'] }) {
      const textStyles = {
        '.ts-heading-xxxs': {
          font: brandSolution.textStyle.heading.xxxs,
        },
        '.ts-heading-xxs': {
          font: brandSolution.textStyle.heading.xxs,
        },
        '.ts-heading-xs': {
          font: brandSolution.textStyle.heading.xs,
        },
        '.ts-heading-sm': {
          font: brandSolution.textStyle.heading.sm,
        },
        '.ts-heading-md': {
          font: brandSolution.textStyle.heading.md,
        },
        '.ts-heading-lg': {
          font: brandSolution.textStyle.heading.lg,
        },
        '.ts-heading-xl': {
          font: brandSolution.textStyle.heading.xl,
        },
        '.ts-heading-xxl': {
          font: brandSolution.textStyle.heading.xxl,
        },
        '.ts-heading-xxxl': {
          font: brandSolution.textStyle.heading.xxxl,
        },
        '.ts-heading-giant': {
          font: brandSolution.textStyle.heading.giant,
        },
        '.ts-heading-display': {
          font: brandSolution.textStyle.heading.display,
        },

        '.ts-subtitle-xxxs': {
          font: brandSolution.textStyle.subtitle.xxxs,
        },
        '.ts-subtitle-xxs': {
          font: brandSolution.textStyle.subtitle.xxs,
        },
        '.ts-subtitle-xs': {
          font: brandSolution.textStyle.subtitle.xs,
        },
        '.ts-subtitle-sm': {
          font: brandSolution.textStyle.subtitle.sm,
        },
        '.ts-subtitle-md': {
          font: brandSolution.textStyle.subtitle.md,
        },
        '.ts-subtitle-lg': {
          font: brandSolution.textStyle.subtitle.lg,
        },
        '.ts-subtitle-xl': {
          font: brandSolution.textStyle.subtitle.xl,
        },

        '.ts-paragraph-xxxs': {
          font: brandSolution.textStyle.paragraph.xxxs,
        },
        '.ts-paragraph-xxs': {
          font: brandSolution.textStyle.paragraph.xxs,
        },
        '.ts-paragraph-xs': {
          font: brandSolution.textStyle.paragraph.xs,
        },
        '.ts-paragraph-sm': {
          font: brandSolution.textStyle.paragraph.sm,
        },

        '.ts-caption-xxxs': {
          font: brandSolution.textStyle.caption.xxxs,
        },
        '.ts-caption-xxs': {
          font: brandSolution.textStyle.caption.xxs,
        },
      }
      addUtilities(textStyles)
    },
    createThemes({
      light: {
        ctx: {
          ...brandSolution.contextualColor.light,
        },
      },
      dark: {
        ctx: {
          ...brandSolution.contextualColor.dark,
        },
      },
    }),
    require('tailwindcss-animate'),
    require('@tailwindcss/typography'),
  ],
}

export default configShare
