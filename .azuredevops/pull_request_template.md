# Descrição

<Por favor descreva todas as mudanças realizadas e problemas endereçados. Além disso, inclua também toda motivação e/ou contexto relevante, listando toda dependência que estiver diretamente envolvida com as mudanças realizadas.>

Essa PR endereça a tarefa #<task id>

Essa PR tem como dependências as tarefas #<task id>, #<task id>, #<task id>...

## Tipo de alterações realizadas

<Marque todas as opções relevantes>

- [ ] Correção de bug (alteração que corrige um problema)
- [ ] Novo recurso (adição de novas funcionalidades)
- [ ] Refatoração (uma mudança no código que não corrige um problema nem adiciona novas funcionalidades)
- [ ] Breaking change (correção ou nova funcionalidade que pode causar mau funcionamento de outras funcionalidades existentes)

## Como testar

<Por favor, descreva quaisquer instruções relevantes para executar, testar e verificar as mudanças feitas.>

## Checklist

- [ ] Meu código segue as diretrizes de estilo especificadas no guia de estilo do projeto
- [ ] Toda UI/UX desenvolvida está de acordo com o protótipo
- [ ] Realizei a revisão do meu próprio código
- [ ] Realizei as alterações necessárias na documentação
- [ ] Minhas mudanças não geram novos "code warnings" ou erros
- [ ] Adicionei testes que cobrem o novo código adicionado
- [ ] Os novos testes e também os já existentes passam localmente com minhas mudanças
