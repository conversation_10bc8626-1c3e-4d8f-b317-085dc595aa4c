# Starter pipeline
# Start with a minimal pipeline that you can customize to build and deploy your code.
# Add steps that build, run tests, deploy, and more:
# https://aka.ms/yaml

trigger:
  batch: true
  branches:
    include:
      - master
      - development
      - homolog

pool:
  vmImage: ubuntu-latest

jobs:
  - job: build
    displayName: 'Build steps'
    steps:
      - task: ArchiveFiles@2
        displayName: Archive files
        inputs:
          rootFolderOrFile: $(System.DefaultWorkingDirectory)
          includeRootFolder: false
          sevenZipCompression: normal
          verbose: true
      - task: PublishBuildArtifacts@1
        displayName: Publish build artifacts
        inputs:
          ArtifactName: published
