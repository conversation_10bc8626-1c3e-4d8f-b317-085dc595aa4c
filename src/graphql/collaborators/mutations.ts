import { gql } from 'graphql-request'

export const DELETE_COLLABORATOR = gql`
  mutation DeleteCollaborator(
    $user_id: Float!
    $enrollment_id: Float!
    $has_to_cancel: Boolean!
  ) {
    deleteUserEnrollment(
      data: {
        user_id: $user_id
        enrollment_id: $enrollment_id
        has_to_cancel: $has_to_cancel
      }
    )
  }
`

export const UPDATE_EMPLOYEE_ROLE = gql`
  mutation updateRoleUser($user_id: Int!, $role_slugs: [Role!]!) {
    updateEmployeeRole(user_id: $user_id, role_slugs: $role_slugs) {
      id
      roles {
        slug
      }
    }
  }
`
