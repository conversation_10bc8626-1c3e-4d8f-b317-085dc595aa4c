import { gql } from 'graphql-request'

export const GET_STUDY_PLANS = gql`
  query getStudyPlans(
    $user_id: Float
    $name: String
    $company_id: Float!
    $limit: Float
    $page: Float
    $onlyPDI: Boolean
    $status: Boolean
    $end_date: DateTime
  ) {
    studyPlans(
      name: $name
      company_id: $company_id
      limit: $limit
      page: $page
      onlyPDI: $onlyPDI
      user_id: $user_id
      status: $status
      end_date: $end_date
    ) {
      data {
        courses_pivot {
          course_id
          course {
            id
            title
          }
        }
        groupsCount
        ai_generated
        end_date
        status
        id
        name
        coursesCount
        squadsCount
        usersCount
        users_completed_count
      }
      total
      perPage
    }
  }
`

export const GET_SPECIFIC_STUDY_PLAN = gql`
  query studyPlan($id: Float!) {
    studyPlan(id: $id) {
      id
      name
      end_date
      description
      courses_pivot {
        course_id
        course {
          id
          title
        }
      }
      squads {
        id
        title
      }
      groups {
        id
        name
      }
      users {
        id
        name
        metadata {
          company_squad {
            title
          }
        }
      }
    }
  }
`
