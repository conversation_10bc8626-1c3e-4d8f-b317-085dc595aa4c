import { gql } from 'graphql-request'

export const CREATE_STUDY_PLAN = gql`
  mutation CreateStudyPlan(
    $name: String!
    $description: String!
    $courses: [StudyPlanCoursePivot!]!
    $company_id: Float!
    $squad_ids: [Float!]
    $user_ids: [Float!]
    $is_pdi: Boolean
    $ai_generated: Boolean
    $is_for_all_users: Boolean
    $is_for_all_squads: Boolean
    $is_for_all_courses: Boolean
  ) {
    createStudyPlan(
      data: {
        name: $name
        description: $description
        courses: $courses
        company_id: $company_id
        squad_ids: $squad_ids
        user_ids: $user_ids
        is_pdi: $is_pdi
        ai_generated: $ai_generated
        is_for_all_users: $is_for_all_users
        is_for_all_squads: $is_for_all_squads
        is_for_all_courses: $is_for_all_courses
      }
    ) {
      id
    }
  }
`

export const CREATE_STUDY_PLAN_V2 = gql`
  mutation CreateStudyPlanV2(
    $name: String!
    $description: String!
    $courses: [StudyPlanCoursePivot!]!
    $company_id: Float!
    $is_pdi: Boolean
    $ai_generated: Boolean
    $user_ids: [Float!]
    $squad_ids: [Float!]
    $group_ids: [Float!]
    $end_date: DateTime
  ) {
    createStudyPlanV2(
      data: {
        name: $name
        description: $description
        courses: $courses
        company_id: $company_id
        is_pdi: $is_pdi
        end_date: $end_date
        user_ids: $user_ids
        squad_ids: $squad_ids
        group_ids: $group_ids
      }
    ) {
      id
    }
  }
`

export const UPDATE_STUDY_PLAN_V2 = gql`
  mutation UpdateStudyPlanV2(
    $id: Float!
    $name: String!
    $description: String!
    $courses: [StudyPlanCoursePivot!]!
    $is_pdi: Boolean
    $end_date: DateTime
    $user_ids: [Float!]
    $squad_ids: [Float!]
    $group_ids: [Float!]
  ) {
    updateStudyPlanV2(
      data: {
        id: $id
        name: $name
        description: $description
        courses: $courses
        is_pdi: $is_pdi
        end_date: $end_date
        user_ids: $user_ids
        squad_ids: $squad_ids
        group_ids: $group_ids
      }
    ) {
      id
      groupsCount
    }
  }
`

export const DELETE_STUDY_PLAN = gql`
  mutation deleteStudyPlan($id: Float!) {
    deleteStudyPlan(id: $id)
  }
`

export const UPDATE_TEAMS_AND_USER_STUDY_PLAN = gql`
  mutation UpdatedTeamsAndUserPlanStudy(
    $id: Float!
    $squad_ids: [Float!]!
    $user_ids: [Float!]!
    $notify: Boolean!
    $is_for_all_users: Boolean
    $is_for_all_squads: Boolean
    $is_for_all_courses: Boolean
  ) {
    updateStudyPlan(
      data: {
        id: $id
        squad_ids: $squad_ids
        user_ids: $user_ids
        notify: $notify
        is_for_all_users: $is_for_all_users
        is_for_all_squads: $is_for_all_squads
        is_for_all_courses: $is_for_all_courses
      }
    ) {
      id
    }
  }
`
