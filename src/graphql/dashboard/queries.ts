import { gql } from 'graphql-request'

export const GET_DASHBOARD_COURSES_RANK = gql`
  query GetDashboardCoursesRank(
    $start_date: DateTime
    $end_date: DateTime
    $company_id: Int
  ) {
    b2bStatistics {
      mostWatchedCoursesRanking(
        start_date: $start_date
        end_date: $end_date
        company_id: $company_id
      ) {
        views
        course {
          title
        }
      }
    }
  }
`

export const GET_DASHBOARD_ENGAGEMENT_RANK = gql`
  query GetDashboardEngagementRank(
    $start_date: DateTime
    $end_date: DateTime
    $company_id: Int
  ) {
    b2bStatistics {
      usersEngagementRanking(
        start_date: $start_date
        end_date: $end_date
        company_id: $company_id
      ) {
        user {
          id
          name
          metadata {
            company_squad {
              title
            }
          }
        }
        activities_completed
        courses_completed(start_date: $start_date, end_date: $end_date)
      }
      usersCourseCompletedRanking(
        start_date: $start_date
        end_date: $end_date
        company_id: $company_id
      ) {
        user {
          id
          name
          metadata {
            company_squad {
              title
            }
          }
        }
        activities_completed(start_date: $start_date, end_date: $end_date)
        courses_completed
      }
    }
  }
`

export const GET_DASHBOARD_ENGAGEMENT_DETAILS = gql`
  query GetDashboardEngagementDetails($id: Int!) {
    user(id: $id) {
      name
      completed: enrolled_courses(course_status: COMPLETED) {
        total
        data {
          title
          user_progress {
            completion_date
          }
        }
      }
      inProgress: enrolled_courses(course_status: IN_PROGRESS) {
        total
        data {
          title
          user_progress {
            completed_activities
            total_activities
          }
        }
      }
    }
  }
`
