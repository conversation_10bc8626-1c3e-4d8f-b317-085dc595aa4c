import { gql } from 'graphql-request'

export const GET_COURSES = gql`
  query getCourses(
    $page: Float
    $all: Boolean!
    $limit: Float
    $q: String
    $available_at: AvailabilityFilter
  ) {
    courses(
      all: $all
      page: $page
      limit: $limit
      q: $q
      available_at: $available_at
    ) {
      data {
        id
        title
        categories {
          title
        }
      }
      total
      perPage
    }
  }
`
