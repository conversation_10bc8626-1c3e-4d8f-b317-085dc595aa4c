'use client'

import { But<PERSON> } from '@ads/components-react'
import Link from 'next/link'
import { HiArrowLeft } from 'react-icons/hi2'

export default function NotFound() {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-ctx-layout-surface">
      <div className="w-full max-w-2xl px-4 text-center">
        <div>
          <div className="mb-6">
            <h1 className="to-ctx-content-subtitle animate-pulse select-none bg-gradient-to-r from-ctx-content-title bg-clip-text text-ctx-content-title text-transparent ts-heading-giant">
              404
            </h1>
            <div className="via-ctx-content-subtitle mx-auto mt-2 h-1 w-20 rounded-full bg-gradient-to-r from-transparent to-transparent opacity-30"></div>
          </div>

          <div className="flex flex-col space-y-4">
            <div className="space-y-2">
              <h2 className="text-ctx-content-title ts-heading-md">
                Oops! Página não encontrada.
              </h2>
              <p className="text-ctx-content-subtitle mx-auto max-w-md leading-relaxed ts-paragraph-xs">
                A página que você está procurando não existe.
              </p>
            </div>

            <div className="mt-6 flex flex-col items-center justify-center gap-3 sm:flex-row">
              <Link href="/dashboard">
                <Button hierarchy="primary" size="md" leadingIcon={HiArrowLeft}>
                  Voltar para o dashboard
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
