'use client'

import {
  Button,
  Dropdown,
  DropdownItem,
  Pagination,
} from '@ads/components-react'
import { Layout, Plus, Table } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { HiOutlineSparkles } from 'react-icons/hi2'
import { useQuery } from 'react-query'
import { useStore } from 'zustand'

import { Filter } from '@/components/filter/filter'
import { PageLayout } from '@/components/layouts/page-layout'
import { CardsSkeleton } from '@/components/loaders/skeletons/cards'
import { StudyPlanTable } from '@/components/tables/study-plan'
import { ToggleSwitch } from '@/components/toggle-switch'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useAuth } from '@/contexts/AuthContext'
import { useIsMobile } from '@/hooks/use-mobile'
import { getStudyPlans } from '@/http/study-plan'
import { StudyPlanData } from '@/model/study-plan'
import { useFilterStore } from '@/store/useFilterStore'

import { CardInfo } from './components/card'
import { EmptyState } from './components/empty-state'

type TrilhasProps = {
  isExpanded: boolean
  setIsExpanded: () => void
}

export function Trilhas({ setIsExpanded }: TrilhasProps) {
  const router = useRouter()
  const { user } = useAuth()
  const isMobile = useIsMobile()

  const { search, status } = useStore(useFilterStore)
  const { onChangeSearch, onChangeStatus } = useFilterStore()
  const [currentPage, setCurrentPage] = useState(1)
  const [openDropdown, setOpenDropdown] = useState(false)
  const [activeTab, setActiveTab] = useState<string>('table')

  const companyId = user?.metadata.company_id ?? 0

  const handleStatus = (): boolean | null => {
    if (status === 'INACTIVE') return false
    if (status === 'FINISHED') return true
    if (status === 'ACTIVE') return true

    return true
  }

  const {
    data: studyPlans,
    isLoading,
    isFetching,
  } = useQuery({
    queryKey: ['getStudyPlansGql', currentPage, companyId, search, status],
    queryFn: () =>
      getStudyPlans({
        page: currentPage,
        company_id: companyId,
        limit: 12,
        name: search ?? '',
        onlyPDI: false,
        user_id: null,
        end_date: status === 'FINISHED' ? new Date() : null,
        status: handleStatus(),
      }),
    refetchOnWindowFocus: false,
    staleTime: 60 * 1000 * 10, // 10 minutos
  })

  useEffect(() => {
    if (isMobile) {
      setActiveTab('card')
    }
  }, [isMobile])

  return (
    <PageLayout
      title="Trilhas de aprendizagem"
      description="Gerencie, acompanhe e analise o progresso de desenvolvimento dos colaboradores em tempo real."
      className="h-[calc(100vh-64px)]"
      actionButton={
        <Dropdown
          trigger={
            <Button size="md" trailingIcon={Plus} className="w-full sm:w-fit">
              Nova Trilha
            </Button>
          }
          align="end"
          open={openDropdown}
          onOpenChange={(open) => setOpenDropdown(open)}
        >
          <div className="flex flex-col gap-4 p-2">
            <Button
              hierarchy="secondary"
              className="w-full"
              onClick={() => router.push('/trilhas-de-aprendizagem/criar')}
            >
              Criar trilha manualmente
            </Button>
            <DropdownItem asChild>
              <Button
                onClick={() => {
                  setOpenDropdown(false)
                  setIsExpanded()
                }}
                trailingIcon={HiOutlineSparkles}
                className="w-full rounded-pill text-black"
              >
                Criar trilha com IA
              </Button>
            </DropdownItem>
          </div>
        </Dropdown>
      }
    >
      <div className="flex items-center gap-4 @container">
        <Filter
          pageKey="trilhas"
          placeholder="Busca por Trilha"
          onChangeStatus={onChangeStatus}
          onChangeSearch={onChangeSearch}
        />

        <ToggleSwitch
          className="hidden md:flex"
          options={[
            {
              value: 'table',
              icon: <Table size={16} />,
              tooltip: 'Visualização em lista',
            },
            {
              value: 'card',
              icon: <Layout size={16} />,
              tooltip: 'Visualização em Cards',
            },
          ]}
          defaultValue={activeTab}
          onChange={(value) => {
            setActiveTab(value)
          }}
        />
      </div>

      {activeTab === 'table' ? (
        <ScrollArea className="h-full min-w-[386px] overflow-x-auto rounded-md pb-14 md:h-[calc(100vh-197px)] [&>div]:py-1">
          <StudyPlanTable
            studyPlans={
              studyPlans?.studyPlans ?? { data: [], total: 0, perPage: 0 }
            }
            isLoading={isLoading}
            isFetchingNewPage={isFetching}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
          />
        </ScrollArea>
      ) : (
        <ScrollArea className="h-[calc(100vh-330px)] rounded-md bg-ctx-layout-body px-2 py-6 @container sm:h-[calc(100vh-280px)] sm:px-6">
          <div className="grid h-full gap-6 @[480px]:grid-cols-2 @[768px]:grid-cols-3">
            {isLoading || isFetching ? (
              <CardsSkeleton />
            ) : studyPlans?.studyPlans.data.length === 0 ? (
              <EmptyState />
            ) : (
              studyPlans?.studyPlans.data.map((studyPlan) => (
                <CardInfo
                  key={studyPlan.id}
                  studyPlanDataInfo={studyPlan as StudyPlanData}
                />
              ))
            )}
          </div>
          <div className="mt-8 flex justify-center">
            {(studyPlans?.studyPlans.data.length as number) > 0 && (
              <Pagination
                custom={{
                  controlNext: isMobile ? 'hidden' : 'block',
                  controlPrevious: isMobile ? 'hidden' : 'block',
                }}
                controlText={{
                  previous: 'Anterior',
                  next: 'Próximo',
                }}
                activeIndex={currentPage - 1}
                setActiveIndex={(e) => setCurrentPage((e as number) + 1)}
                pageAmount={Math.ceil(
                  (studyPlans?.studyPlans?.total as number) /
                    (studyPlans?.studyPlans?.perPage as number)
                )}
                ellipsisLabel="Buscar mais itens"
              />
            )}
          </div>
        </ScrollArea>
      )}
    </PageLayout>
  )
}
