'use client'

import { Suspense } from 'react'

import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '@/components/ui/resizable'
import {
  LearningPathsProvider,
  useLearningPaths,
} from '@/contexts/LearningPathsContext'
import { useIsMobile } from '@/hooks/use-mobile'
import { useChatStore } from '@/store/useChatStore'

import { ChatPanel } from './chat'
import { Trilhas } from './trilha'

function LearningPathsContent() {
  const isMobile = useIsMobile()
  const { chatId: chatIdFromStore } = useChatStore()

  const {
    isExpanded,
    isClosed,
    leftPanelRef,
    handleExpandToggle,
    handleLeftPanelCollapse,
    handleLeftPanelExpand,
    handleClose,
    handleCreateWithAI,
  } = useLearningPaths()

  if (isMobile) {
    return (
      <div className="flex h-full flex-col">
        {isClosed ? (
          <div className="flex-1">
            <Trilhas
              isExpanded={isExpanded}
              setIsExpanded={handleCreateWithAI}
            />
          </div>
        ) : (
          <div className="flex-1">
            <ChatPanel
              isExpanded={isExpanded}
              chatIdFromQuery={chatIdFromStore}
              onExpandToggle={handleExpandToggle}
              onClose={handleClose}
            />
          </div>
        )}
      </div>
    )
  }

  return (
    <ResizablePanelGroup direction="horizontal">
      <ResizablePanel
        ref={leftPanelRef}
        defaultSize={isClosed || isExpanded ? 0 : 75}
        minSize={30}
        maxSize={85}
        collapsible={true}
        collapsedSize={0}
        onCollapse={handleLeftPanelCollapse}
        onExpand={handleLeftPanelExpand}
      >
        <Trilhas isExpanded={isExpanded} setIsExpanded={handleCreateWithAI} />
      </ResizablePanel>

      {!isClosed && (
        <>
          <ResizableHandle className="panel-resize-handle border-l border-ctx-layout-border" />

          <ResizablePanel
            defaultSize={100}
            minSize={35}
            maxSize={100}
            className="panel-transition"
          >
            <ChatPanel
              isExpanded={isExpanded}
              onExpandToggle={handleExpandToggle}
              onClose={handleClose}
            />
          </ResizablePanel>
        </>
      )}
    </ResizablePanelGroup>
  )
}

export default function LearningPaths() {
  return (
    <LearningPathsProvider>
      <Suspense
        fallback={
          <div className="flex h-screen items-center justify-center">
            <div className="text-muted-foreground">Carregando...</div>
          </div>
        }
      >
        <LearningPathsContent />
      </Suspense>
    </LearningPathsProvider>
  )
}
