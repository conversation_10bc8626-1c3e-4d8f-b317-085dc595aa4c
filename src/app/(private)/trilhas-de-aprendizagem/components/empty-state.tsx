'use client'
import { IconShape } from '@ads/components-react'
import { Search } from 'lucide-react'

export function EmptyState() {
  return (
    <div className="mx-auto flex h-[calc(100vh-310px)] w-full flex-col items-center justify-center gap-4 rounded-2xl bg-ctx-layout-body">
      <IconShape
        className="bg-ctx-highlight-promote"
        type="brand"
        size="md"
        icon={() => <Search size={28} className="text-base-gray-900" />}
      />
      <div>
        <h4 className="text-center text-ctx-content-title ts-heading-xs">
          Sem plano de estudos
        </h4>
        <p className="mt-1 text-center text-ctx-content-base ts-paragraph-xxs">
          Você ainda não criou nenhum Plano de estudo <br /> dentro da trilha de
          aprendizagem.
        </p>
      </div>
    </div>
  )
}
