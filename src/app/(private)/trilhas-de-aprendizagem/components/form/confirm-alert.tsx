import { Button } from '@ads/components-react'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Separator } from '@/components/ui/separator'

type ConfirmStudyPlanAlertProps = {
  handleConfirm: () => void
  isEditMode: boolean
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  isLoading: boolean
}

export function ConfirmStudyPlanAlert({
  isEditMode,
  isOpen,
  onOpenChange,
  isLoading,
  handleConfirm,
}: ConfirmStudyPlanAlertProps) {
  return (
    <AlertDialog open={isOpen} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {isEditMode ? 'Edição' : 'Cria<PERSON>'} de trilha
          </AlertDialogTitle>
          <AlertDialogDescription>
            Ao confirmar, os colaboradores selecionados receberão um email
            notificando que uma nova trilha foram atribuidas a eles.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <Separator />
        <AlertDialogFooter className="h-20 flex-col gap-2 sm:h-full sm:flex-row">
          <AlertDialogCancel asChild>
            <Button hierarchy="secondary" className="w-full flex-1">
              Cancelar
            </Button>
          </AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button
              onClick={handleConfirm}
              isLoading={isLoading}
              hierarchy="primary"
              className="w-full flex-1"
            >
              {isEditMode ? 'Salvar' : 'Cadastrar'}
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
