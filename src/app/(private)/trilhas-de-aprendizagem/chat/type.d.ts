export interface Course {
  id: number
  title: string
  justification: string
  description: string
  image_url: string
}

export interface ChatMessage {
  id: string
  type: 'user' | 'ai'
  content: string
  timestamp: Date
  metadata?: {
    courses?: Course[]
    selectedCourses?: string[]
    studyPlanCreated?: boolean
    showCoursesSelection?: boolean
    isNamingStep?: boolean
    showAssignButton?: boolean
    showConclusionButtons?: boolean
  }
}
