import { useChatStore } from '@/store/useChatStore'

interface Chat {
  id: string | number
  title: string
}

interface ChatListProps {
  label: string
  chats: Chat[]
  onChatSelect?: () => void
}

export function ChatList({ label, chats, onChatSelect }: ChatListProps) {
  const { setChatId } = useChatStore()

  const handleChatSelect = (chatId: string | number) => {
    setChatId(String(chatId))
    onChatSelect?.()
  }

  return (
    <div className="flex flex-col gap-2">
      <span className="text-ctx-content-base ts-heading-xxxs">{label}</span>

      <div className="flex w-full cursor-pointer flex-col">
        {chats.map((chat) => (
          <div
            key={chat.id}
            onClick={() => handleChatSelect(chat.id)}
            className="flex items-center justify-between rounded-full px-8 py-2 hover:bg-ctx-interactive-secondary"
          >
            <p className="text-ctx-content-base ts-paragraph-xxs">
              {chat.title}
            </p>
            {/* ToDo: Implementar botão que, ao ser clicado, abra um menu com opções para o chat. (Funcionalidade prevista como melhoria futura). */}
            {/* <Dropdown
              align="end"
              trigger={
                <IconButton
                  ariaLabel="Mais opções"
                  icon={EllipsisVertical}
                  size="md"
                  hierarchy="tertiary"
                />
              }
            >
              <DropdownItem>
                <DropdownItemContent leadingIcon={SquarePenIcon}>
                  Renomear
                </DropdownItemContent>
              </DropdownItem>
              <DropdownItem status="destructive">
                <DropdownItemContent leadingIcon={TrashIcon}>
                  Excluir
                </DropdownItemContent>
              </DropdownItem>
            </Dropdown> */}
          </div>
        ))}
      </div>
    </div>
  )
}
