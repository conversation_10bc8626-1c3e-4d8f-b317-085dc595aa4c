'use client'

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Toolt<PERSON> } from '@ads/components-react'
import {
  ClockIcon,
  ExpandIcon,
  MessageCircleIcon,
  ShrinkIcon,
  X,
} from 'lucide-react'
import { useState } from 'react'

import { ToggleSwitch } from '@/components/toggle-switch'
import { Separator } from '@/components/ui/separator'

import { ChatAI } from './chat-ai'
import { ChatForm } from './chat-form'
import { ChatHistory } from './chat-history'

interface ChatPanelProps {
  isExpanded: boolean
  chatIdFromQuery: string | null
  onExpandToggle: () => void
  onClose: () => void
}

export function ChatPanel({
  isExpanded,
  chatIdFromQuery,
  onExpandToggle,
  onClose,
}: ChatPanelProps) {
  const [activeTab, setActiveTab] = useState<string>('chat')

  return (
    <div className="flex h-[calc(100vh-64px)] flex-col">
      <header className="hidden space-y-6 bg-ctx-layout-body p-4 md:block">
        <section className="flex items-center justify-between space-x-2">
          <Tooltip side="right" title={isExpanded ? 'Recolher' : 'Expandir'}>
            <IconButton
              size="sm"
              ariaLabel={isExpanded ? 'recolher' : 'expandir'}
              hierarchy="secondary"
              icon={isExpanded ? ShrinkIcon : ExpandIcon}
              onClick={onExpandToggle}
            />
          </Tooltip>

          <div className="flex items-center space-x-4">
            <ToggleSwitch
              options={[
                { value: 'chat', icon: <MessageCircleIcon size={20} /> },
                { value: 'history', icon: <ClockIcon size={20} /> },
              ]}
              defaultValue={activeTab}
              onChange={(value) => {
                setActiveTab(value)
              }}
            />

            <IconButton
              size="lg"
              ariaLabel="fechar"
              hierarchy="secondary"
              icon={X}
              onClick={onClose}
            />
          </div>
        </section>

        <div>
          {activeTab === 'chat' ? (
            <>
              <h1 className="text-ctx-content-title ts-heading-md">
                Planejar trilha
              </h1>

              <p className="text-ctx-content-base ts-paragraph-xs">
                Descreva seus interesses e desafios para criarmos uma trilha de
                aprendizagem exclusiva
              </p>
            </>
          ) : (
            <h1 className="text-ctx-content-title ts-heading-md">
              Histórico do chat
            </h1>
          )}
        </div>

        <Separator />
      </header>

      <header className="block space-y-6 bg-ctx-layout-body p-4 md:hidden">
        <section className="flex items-center justify-between">
          <div>
            {activeTab === 'chat' ? (
              <>
                <h1 className="text-ctx-content-title ts-heading-md">
                  Planejar trilha
                </h1>
              </>
            ) : (
              <h1 className="text-ctx-content-title ts-heading-md">
                Histórico do chat
              </h1>
            )}
          </div>

          <div className="flex items-center space-x-4">
            <ToggleSwitch
              options={[
                { value: 'chat', icon: <MessageCircleIcon size={20} /> },
                { value: 'history', icon: <ClockIcon size={20} /> },
              ]}
              defaultValue={activeTab}
              onChange={(value) => {
                setActiveTab(value)
              }}
            />

            <IconButton
              size="lg"
              ariaLabel="fechar"
              hierarchy="secondary"
              icon={X}
              onClick={onClose}
            />
          </div>
        </section>

        <Separator />
      </header>

      {activeTab === 'chat' ? (
        chatIdFromQuery ? (
          <ChatAI />
        ) : (
          <ChatForm />
        )
      ) : (
        <ChatHistory onChatSelect={() => setActiveTab('chat')} />
      )}
    </div>
  )
}
