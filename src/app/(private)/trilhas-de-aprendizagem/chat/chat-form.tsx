'use client'

import { IconShape, useAlert } from '@ads/components-react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { HiOutlineSparkles } from 'react-icons/hi2'
import { useMutation, useQueryClient } from 'react-query'

import { InputChat } from '@/components/ui/input-chat'
import { Skeleton } from '@/components/ui/skeleton'
import { useAuth } from '@/contexts/AuthContext'
import { postChatMessageAI } from '@/http/chat-ai/post-chat-ai'
import { useChatStore } from '@/store/useChatStore'
import { type ChatSchema, chatSchema } from '@/validations/chat'

const loadingMessages = [
  'Analisando seu desafio...',
  'Buscando os melhores cursos para você...',
  'Organizando o conteúdo ideal...',
]

export function ChatForm() {
  const queryClient = useQueryClient()
  const { user, isLoading } = useAuth()
  const { setChatId } = useChatStore()

  const { alert } = useAlert()

  const [currentMessageIndex, setCurrentMessageIndex] = useState(0)

  const { register, handleSubmit, watch } = useForm<ChatSchema>({
    resolver: zodResolver(chatSchema),
    defaultValues: {
      context: '',
    },
  })

  const contextValue = watch('context')

  const { mutate, isLoading: isMutating } = useMutation({
    mutationFn: (data: ChatSchema) => postChatMessageAI(data),
    onSuccess: (response) => {
      queryClient.invalidateQueries(['history-chats'])
      setChatId(response.chat_id)
    },
    onError: () => {
      alert({
        title: 'Erro ao criar trilha de aprendizado',
        description:
          'Ocorreu um erro ao criar a trilha de aprendizado. Tente novamente mais tarde.',
        alertType: 'danger',
      })
    },
  })

  function handleKeyDown(
    e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(onSubmit)()
    }
  }

  function onSubmit(data: ChatSchema) {
    setCurrentMessageIndex(0)
    mutate(data)
  }

  useEffect(() => {
    if (!isMutating) return
    const interval = setInterval(() => {
      setCurrentMessageIndex((prev) => (prev + 1) % loadingMessages.length)
    }, 5000)
    return () => clearInterval(interval)
  }, [isMutating])

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="flex w-full flex-1 flex-col justify-center space-y-8 bg-ctx-layout-body p-2"
    >
      <div className="flex w-full flex-col items-center justify-center">
        <IconShape
          className="bg-ctx-highlight-promote text-base-gray-900"
          type="brand"
          size="md"
          icon={() => <HiOutlineSparkles size={26} />}
        />

        {isLoading ? (
          <Skeleton className="mt-8 h-8 w-1/3" />
        ) : (
          <h1 className="mt-8 text-center text-ctx-content-title ts-heading-sm md:ts-heading-md">
            Olá, {user?.name}! <br /> Pronto para criar uma nova trilha?
          </h1>
        )}
      </div>

      <div className="mx-auto flex w-full max-w-[800px] flex-col items-center justify-center space-y-8">
        <InputChat
          disabled={isMutating}
          placeholder="Digite um tema, habilidade ou necessidade da sua equipe. Ex.: vendas, gestão de projetos, comunicação."
          onKeyDown={handleKeyDown}
          onClickSendMessage={() => handleSubmit(onSubmit)()}
          disabledButtonMessage={contextValue?.length <= 0 || isMutating}
          {...register('context')}
        />

        {isMutating && (
          <div className="flex flex-col items-center space-y-4">
            <div className="flex items-center space-x-2">
              <div className="h-2 w-2 animate-bounce rounded-full bg-ctx-highlight-promote [animation-delay:-0.3s]"></div>
              <div className="h-2 w-2 animate-bounce rounded-full bg-ctx-highlight-promote [animation-delay:-0.15s]"></div>
              <div className="h-2 w-2 animate-bounce rounded-full bg-ctx-highlight-promote"></div>
            </div>

            <p className="text-ctx-content-subtitle text-center transition-all duration-300 ease-in-out">
              {loadingMessages[currentMessageIndex]}
            </p>
          </div>
        )}

        <div>
          <p className="text-center text-ctx-content-base ts-heading-xxs">
            Escreva um desafio. <br /> A IA analisa, sugere cursos e você
            atribui em poucos cliques.
          </p>
        </div>
      </div>
    </form>
  )
}
