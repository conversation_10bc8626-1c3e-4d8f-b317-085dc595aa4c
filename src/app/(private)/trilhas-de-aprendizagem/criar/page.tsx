'use client'

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  StepperI<PERSON>,
  useStepper,
} from '@ads/components-react'
import { zodResolver } from '@hookform/resolvers/zod'
import { FormProvider, useForm } from 'react-hook-form'
import Cookies from 'universal-cookie'

import { useSelectedCoursesTrail } from '@/store/useSelectedCoursesTrail'
import { cookieSettings } from '@/utils/cookie-settings'

import { CancelNewStudyPlanAlert } from './components/cancel-alert'
import { Step1 } from './step1'
import { Step2 } from './step2'
import { CombinedCheckoutSchema, CombinedCheckoutType } from './validations'

export default function CriarTrilha() {
  const { activeStep, previousStep, nextStep } = useStepper({
    initialStep: 0,
    stepAmount: 4,
  })

  const coursesSelected = useSelectedCoursesTrail(
    (state) => state.coursesSelected
  )
  const cookies = new Cookies(null, { path: '/' })

  const methods = useForm<CombinedCheckoutType>({
    resolver: zod<PERSON><PERSON><PERSON><PERSON>(CombinedCheckoutSchema),
  })

  const handleSubmit = (data: CombinedCheckoutType) => {
    if (activeStep === 0 && !!methods.formState.errors) {
      cookies.set('step1', JSON.stringify(data), cookieSettings())
      nextStep()
      return
    }
    if (activeStep === 1) {
      cookies.set('step2', coursesSelected, cookieSettings())
      // nextStep()
      // TODO: Remove console and add logic nextStep
      console.log(coursesSelected)
      return
    }
  }

  return (
    <FormProvider {...methods}>
      <form
        className="m-auto flex w-full flex-col gap-8 px-2 py-4 lg:p-4"
        onSubmit={methods.handleSubmit(handleSubmit)}
      >
        <div>
          <h1 className="text-ctx-content-title ts-heading-md">
            Trilha de aprendizagem
          </h1>
          <span className="text-ctx-content-base ts-paragraph-xs">
            Gerencie, acompanhe e analise o progresso de desenvolvimento dos
            colaboradores em tempo real.
          </span>
        </div>
        <div className="flex w-full justify-center">
          <Stepper activeStep={activeStep}>
            <StepperItem title="Informações" />
            <StepperItem title="Selecionar cursos" />
            <StepperItem title="Selecionar participantes" />
            <StepperItem title="Resumo" />
          </Stepper>
        </div>
        <div>
          <StepperContent activeStep={activeStep} index={0}>
            <Step1 />
          </StepperContent>
          <StepperContent activeStep={activeStep} index={1}>
            <Step2 />
          </StepperContent>
          <StepperContent activeStep={activeStep} index={2}>
            <p>Content 3</p>
          </StepperContent>
          <StepperContent activeStep={activeStep} index={3}>
            <p>Content 4</p>
          </StepperContent>
        </div>

        <div className="flex w-full justify-end gap-2">
          {activeStep === 0 ? (
            <CancelNewStudyPlanAlert />
          ) : (
            <Button type="button" hierarchy="tertiary" onClick={previousStep}>
              Voltar
            </Button>
          )}

          <Button type="submit">Próximo</Button>
        </div>
      </form>
    </FormProvider>
  )
}
