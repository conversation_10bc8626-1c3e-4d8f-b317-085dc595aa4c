'use client'

import { Search } from '@ads/components-react'
import { useEffect, useState } from 'react'
import { HiOutlineClipboardDocumentCheck } from 'react-icons/hi2'
import { useQuery } from 'react-query'

import { CoursesTable } from '@/components/tables/study-plan/courses'
import { ESolutionContext } from '@/enum/solution-context'
import { useDebouncedValue } from '@/hooks/use-debounce'
import { getCourses } from '@/http/courses'

import { EmptyState } from '../../components/empty-state'

export function Step2() {
  const [currentPage, setCurrentPage] = useState(1)
  const [searchValue, setSearchValue] = useState('')

  const debouncedSearchValue = useDebouncedValue(searchValue, 500)

  const {
    data: b2bCourses,
    isFetching,
    isLoading,
  } = useQuery({
    queryKey: ['studyPlanCourses', searchValue, currentPage],
    queryFn: () =>
      getCourses({
        page: currentPage,
        limit: 10,
        q: searchValue || undefined,
        all: false,
        available_at: ESolutionContext.PRO.toLowerCase() as ESolutionContext,
      }),
    getNextPageParam: (response, allPagesData) => {
      const nextPage = allPagesData.length + 1
      return nextPage <= Math.ceil(response.courses.total / 10)
        ? nextPage
        : undefined
    },
    keepPreviousData: false,
    staleTime: 60 * 1000 * 10,
    cacheTime: 0,
  })

  useEffect(() => {
    setCurrentPage(1)
  }, [debouncedSearchValue, setCurrentPage])

  return (
    <div className="space-y-3 rounded-lg bg-ctx-layout-body p-4 lg:p-8">
      <div className="flex justify-between">
        <h1 className="flex items-center gap-2 text-ctx-content-title ts-heading-sm">
          <HiOutlineClipboardDocumentCheck />
          Seleção de Cursos
        </h1>

        <div className="w-full max-w-[300px]">
          <Search
            size="md"
            placeholder="Busque por nome do curso"
            className="bg-ctx-interactive-secondary [&_button.at-rounded-component-iconButton-border-radius]:hidden"
            handleChange={(e) => setSearchValue(e)}
            query={searchValue}
          />
        </div>
      </div>

      <section className="pt-6">
        {b2bCourses?.courses.data.length === 0 && !isLoading && !isFetching ? (
          <EmptyState />
        ) : (
          <CoursesTable
            isLoading={isLoading}
            isFetchingNewPage={isFetching}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            courses={b2bCourses?.courses ?? { data: [], total: 0, perPage: 0 }}
          />
        )}
      </section>
    </div>
  )
}
