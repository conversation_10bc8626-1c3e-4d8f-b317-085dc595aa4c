'use client'

import { Checkbox, Textarea } from '@ads/components-react'
import { useEffect } from 'react'
import { Controller, useFormContext } from 'react-hook-form'
import { HiOutlineClipboardDocumentCheck } from 'react-icons/hi2'
import Cookies from 'universal-cookie'

import { DatePicker } from '@/components/ui/date-picker'
import { InputField } from '@/components/ui/input-form'
import { SelectAds } from '@/components/ui/select-ads'

import { CombinedCheckoutType } from '../validations'

export function Step1() {
  const cookies = new Cookies(null, { path: '/' })

  const {
    watch,
    register,
    control,
    formState: { errors },
    resetField,
    setValue,
  } = useFormContext<CombinedCheckoutType>()

  useEffect(() => {
    const step1 = cookies.get('step1')
    if (step1) {
      setValue('name', step1.name)
      setValue('description', step1.description)
      setValue('isMandatory', step1.isMandatory)
      setValue('duration', step1.duration)
      setValue('startDate', step1.startDate)
      setValue('endDate', step1.endDate)
    }
  }, [])

  return (
    <div className="space-y-3 rounded-lg bg-ctx-layout-body p-4 lg:p-8">
      <h1 className="flex items-center gap-2 text-ctx-content-title ts-heading-sm">
        <HiOutlineClipboardDocumentCheck />
        Informações Básicas
      </h1>

      <section className="space-y-8 rounded-xl border border-ctx-layout-border p-4">
        <InputField
          id="name"
          label="Nome"
          type="text"
          placeholder="Nome da trilha de aprendizagem"
          className="w-full"
          {...register('name')}
          errorMessage={errors.name?.message}
        />

        <Textarea
          fullWidth
          label="Descrição"
          placeholder="Descrição da trilha de aprendizagem"
          custom={{
            textarea: 'bg-ctx-interactive-secondary',
          }}
          rows={6}
          {...register('description')}
        />

        <div className="space-y-4">
          <Controller
            name="isMandatory"
            control={control}
            render={({ field }) => (
              <Checkbox
                className="w-[130px]"
                label="Trilha obrigatória"
                custom={{
                  item: 'bg-ctx-interactive-secondary',
                }}
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            )}
          />
          <div className="flex w-full flex-col gap-4 lg:gap-8 xl:flex-row xl:items-center">
            {watch('isMandatory') && (
              <Controller
                name="duration"
                control={control}
                render={({ field }) => (
                  <div className="h-20 min-w-[208px] lg:w-[280px]">
                    <SelectAds
                      custom={{
                        trigger: 'h-10 bg-ctx-interactive-secondary',
                      }}
                      options={[
                        { value: '1', label: '1 mês' },
                        { value: '2', label: '3 meses' },
                        { value: '3', label: '6 meses' },
                        { value: '4', label: 'Personalizado' },
                      ]}
                      label="Selecionar duração"
                      placeholder="Carga horária"
                      onValueChange={(event) => {
                        field.onChange(event)
                        resetField('startDate')
                        resetField('endDate')
                      }}
                      hasError={!!errors.duration}
                      defaultValue={field.value}
                      value={field.value}
                    />
                    <span className="text-red-500 ts-subtitle-xxxs">
                      {errors.duration?.message}
                    </span>
                  </div>
                )}
              />
            )}

            {watch('duration') === '4' && watch('isMandatory') && (
              <div className="flex w-full flex-col lg:flex-row lg:items-center lg:gap-4">
                <Controller
                  name="startDate"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      label="Data de início"
                      placeholder="Selecione a Data de início"
                      value={field.value as Date}
                      onChange={field.onChange}
                      errorMessage={errors.startDate?.message}
                      classNameButton="w-[250px]"
                    />
                  )}
                />

                <span className="mb-2 text-ctx-content-base ts-paragraph-xxs lg:mb-0">
                  até
                </span>

                <Controller
                  name="endDate"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      label="Data de término"
                      placeholder="Selecione a Data de término"
                      value={field.value as Date}
                      errorMessage={errors.endDate?.message}
                      onChange={field.onChange}
                      classNameButton="w-[250px]"
                    />
                  )}
                />
              </div>
            )}
          </div>
        </div>
      </section>
    </div>
  )
}
