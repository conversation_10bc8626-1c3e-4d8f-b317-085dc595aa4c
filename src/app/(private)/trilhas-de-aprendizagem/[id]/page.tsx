'use client'

import { useAlert } from '@ads/components-react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useMutation, useQueryClient } from 'react-query'

import { updateStudyPlanV2 } from '@/http/study-plan'

import { FormStudyPlan } from '../components/form'
import { getDatePlusDays } from '../components/form/utils'
import { FormStudyPlanType } from '../components/form/validations'
import { StudyPlanQueriesProvider } from '../contexts/StudyPlanQueriesContext'

export default function EditarTrilha() {
  const queryClint = useQueryClient()
  const params = useParams()
  const { alert } = useAlert()
  const router = useRouter()

  const { mutate: updateStudyPlanMutation, isLoading: isCreatingStudyPlan } =
    useMutation({
      mutationFn: (data: FormStudyPlanType) =>
        updateStudyPlanV2({
          id: Number(params.id),
          name: data.name,
          description: data.description || '',
          is_pdi: false,
          courses: data.courses.map((course, index) => ({
            course_id: Number(course.value),
            order: index,
          })),
          end_date: data.isMandatory
            ? data.duration === '4'
              ? data.endDate
              : getDatePlusDays(Number(data.duration))
            : null,
          squad_ids:
            data.squads?.length !== 0 && data.squads
              ? data.squads.map((squad) => Number(squad.value))
              : null,
          user_ids:
            data.collaborators.length === 0
              ? null
              : data.collaborators.map((collaborator) =>
                  Number(collaborator.value)
                ),
          group_ids:
            data.groups.length === 0
              ? null
              : data.groups.map((group) => Number(group.value)),
        }),
      onSuccess: () => {
        queryClint.invalidateQueries(['getStudyPlansGql'])
        alert({
          title: 'Trilha de aprendizado atualizada com sucesso!',
          description: 'A trilha foi atualizada com sucesso.',
          alertType: 'success',
        })
        router.push('/trilhas-de-aprendizagem')
      },
      onError: () => {
        alert({
          title: 'Erro ao atualizar trilha de aprendizado',
          description:
            'Ocorreu um erro ao atualizar a trilha de aprendizado. Tente novamente mais tarde.',
          alertType: 'danger',
        })
      },
    })

  const handleSubmit = (data: FormStudyPlanType) => {
    updateStudyPlanMutation(data)
  }

  return (
    <StudyPlanQueriesProvider>
      <div className="flex w-full flex-col gap-4 rounded-lg p-8">
        <div>
          <h1 className="text-ctx-content-title ts-heading-md">
            Trilha de aprendizagem
          </h1>
          <span className="text-ctx-content-base ts-paragraph-xs">
            Gerencie, acompanhe e analise o progresso de desenvolvimento dos
            colaboradores em tempo real.
          </span>
        </div>

        <FormStudyPlan
          handleSubmitForm={handleSubmit}
          isLoading={isCreatingStudyPlan}
          isEditMode
        />
      </div>
    </StudyPlanQueriesProvider>
  )
}
