'use client'

import { But<PERSON>, IconShape } from '@ads/components-react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { Controller, type SubmitHandler, useForm } from 'react-hook-form'
import { LuBriefcaseBusiness } from 'react-icons/lu'
import { useQuery } from 'react-query'
import Cookies from 'universal-cookie'

import { Header } from '@/components/layouts/header/header'
import { SelectAds } from '@/components/ui/select-ads'
import { useAuth } from '@/contexts/AuthContext'
import { getCompaniesBySelect } from '@/http/companies/get-companies'
import { useCompanyStore } from '@/store/useCompanyStore'

import type { SubmitSelectedCompany } from './validations'

export default function SelectCompany() {
  const { setCompanyId } = useCompanyStore()
  const { push } = useRouter()
  const { user } = useAuth()

  const { data: companies } = useQuery({
    queryKey: ['companies'],
    queryFn: () => getCompaniesBySelect(false),
    keepPreviousData: true,
    staleTime: 60 * 1000 * 10,
  })

  const { control, handleSubmit, watch } = useForm<SubmitSelectedCompany>({
    defaultValues: {
      company: '',
    },
  })

  const accessAllowed = user?.roles.some((role) => role.id === 1) ?? false
  const selectedCompany = watch('company')

  const handleSubmitCompany: SubmitHandler<SubmitSelectedCompany> = (data) => {
    const companyId = Number(data.company)
    const cookies = new Cookies(null, { path: '/' })
    const b2bCompanyId = cookies.get('b2bCompanyId')

    if (b2bCompanyId) {
      cookies.remove('b2bCompanyId')
    }
    cookies.set('b2bCompanyId', JSON.stringify(companyId), {
      maxAge: 60 * 60 * 6, // 6 hours
    })

    setCompanyId(companyId)

    push('/colaboradores')
  }

  useEffect(() => {
    if (!accessAllowed && user) {
      push('/404')
    }
  }, [user])

  return (
    accessAllowed && (
      <form onSubmit={handleSubmit(handleSubmitCompany)}>
        <div className="fixed inset-0 z-10 flex flex-col bg-ctx-layout-surface">
          <div className="flex-shrink-0">
            <Header />
          </div>

          <div className="flex flex-1 items-center justify-center px-4 md:px-2">
            <div className="flex w-full max-w-[650px] flex-col items-center justify-center space-y-12">
              <div className="flex flex-col items-center justify-center space-y-5">
                <IconShape
                  size="md"
                  icon={() => <LuBriefcaseBusiness />}
                  type="brand"
                  className="bg-ctx-highlight-promote"
                />

                <div className="flex flex-col items-center justify-center space-y-2">
                  <h1 className="text-ctx-content-title ts-heading-md">
                    Selecione uma Empresa
                  </h1>

                  <p className="text-ctx-content-base ts-paragraph-xs">
                    Escolha a empresa que deseja gerenciar
                  </p>
                </div>
              </div>

              <div className="flex w-full flex-col space-y-4 md:flex-row md:space-x-6 md:space-y-0">
                <Controller
                  name="company"
                  control={control}
                  render={({ field }) => (
                    <SelectAds
                      label=""
                      size="md"
                      fullWidth
                      placeholder="Selecione uma Empresa"
                      custom={{
                        content: 'z-50',
                      }}
                      onValueChange={field.onChange}
                      options={
                        companies?.map((item) => ({
                          value: String(item.value),
                          label: item.label,
                        })) ?? []
                      }
                    />
                  )}
                />

                <Button
                  className="w-full md:w-auto"
                  type="submit"
                  disabled={!selectedCompany}
                  hierarchy="primary"
                  size="md"
                >
                  Ir para área gerencial
                </Button>
              </div>
            </div>
          </div>
        </div>
      </form>
    )
  )
}
