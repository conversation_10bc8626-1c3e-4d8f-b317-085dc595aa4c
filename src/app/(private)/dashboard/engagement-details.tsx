import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rollArea } from '@ads/components-react'
import { DialogTitle } from '@radix-ui/react-dialog'
import { Eye, X } from 'lucide-react'
import { useState } from 'react'
import { useQuery } from 'react-query'

import { SkeletonEngagementDetails } from '@/components/loaders/skeletons/engagement-details'
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerTrigger,
} from '@/components/ui/drawer'
import { getEngagementDetails } from '@/http/dashboard/get-engagement-details'
import { cn } from '@/lib/utils'
import { convertDateToCalendar } from '@/utils/convert-date'

interface EngagementDetailsProps {
  user: {
    id: number
    name: string
  }
}

type TabType = 'FINISH' | 'IN-PROGRESS'

export function EngagementDetails({ user }: EngagementDetailsProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [activeTab, setActiveTab] = useState<TabType>('FINISH')

  const { data: details, isLoading } = useQuery({
    queryKey: ['getEngamentDetails', user.id, isOpen],
    queryFn: () =>
      getEngagementDetails({
        id: user.id,
      }),
    keepPreviousData: false,
    enabled: isOpen && !!user.id,
  })

  const tabs: { label: string; total?: number; value: TabType }[] = [
    {
      label: 'Finalizados',
      value: 'FINISH',
      total: details?.user.completed?.total,
    },
    {
      label: 'Em Progresso',
      value: 'IN-PROGRESS',
      total: details?.user.inProgress?.total,
    },
  ]

  const data = {
    FINISH: details?.user.completed?.data || [],
    'IN-PROGRESS': details?.user.inProgress?.data || [],
  }[activeTab]

  const isLoadingData = isLoading
  const isEmpty = !isLoadingData && (!details || data.length === 0)

  return (
    <Drawer direction="right" open={isOpen} onOpenChange={setIsOpen}>
      <DrawerTrigger asChild>
        <IconButton
          size="sm"
          icon={Eye}
          ariaLabel="Exibir detalhes"
          hierarchy="tertiary"
          className="[&>svg]:h-5 [&>svg]:w-5"
        />
      </DrawerTrigger>
      <DrawerContent
        data-vaul-no-drag
        className="left-auto h-screen w-full max-w-[520px] overflow-hidden rounded-none"
      >
        <ScrollArea className="relative h-full w-full bg-ctx-layout-body">
          <DrawerClose asChild>
            <IconButton
              ariaLabel="Fechar detalhes"
              className="absolute right-2 top-2 z-10 sm:right-5 sm:top-7"
              hierarchy="tertiary"
              size="lg"
              icon={X}
            />
          </DrawerClose>

          <div className="p-5 sm:p-10">
            <DialogTitle className="text-ctx-content-title ts-heading-md">
              Detalhes do Engajamento
            </DialogTitle>
            <p className="mt-2 text-ctx-content-title ts-heading-xs">
              Usuário: {user.name}
            </p>

            <div className="mt-8 flex items-center border-b-2 border-[#D2D2D2]">
              {tabs.map(({ label, value, total }) => (
                <button
                  key={value}
                  className={cn(
                    'relative pb-1 pr-4 text-ctx-content-base ts-heading-xs',
                    activeTab === value &&
                      'text-ctx-content-title ts-subtitle-xs'
                  )}
                  onClick={() => setActiveTab(value)}
                >
                  {label} {total && `(${total})`}
                  {activeTab === value && (
                    <div className="absolute bottom-0 left-0 h-[2px] w-full translate-y-[100%] bg-ctx-interactive-primary" />
                  )}
                </button>
              ))}
            </div>

            <div className="mt-7 flex flex-col">
              {isLoadingData && <SkeletonEngagementDetails />}

              {isEmpty && (
                <p className="text-center text-ctx-content-base ts-heading-xs">
                  {activeTab === 'FINISH' && 'Nenhum curso foi finalizado'}
                  {activeTab === 'IN-PROGRESS' && 'Nenhum curso foi iniciado'}
                </p>
              )}

              {!isEmpty &&
                data?.map(({ title, user_progress }) => (
                  <div
                    key={title}
                    className="flex flex-col border-b-2 border-[#E4E4E7] py-3"
                  >
                    <p className="text-ctx-content-title ts-subtitle-xs">
                      {title}
                    </p>
                    <p className="text-ctx-content-base ts-heading-xxs">
                      {activeTab === 'FINISH' &&
                        'completion_date' in user_progress && (
                          <p className="text-ctx-content-base ts-heading-xxs">
                            Finalizado em:{' '}
                            {convertDateToCalendar(
                              user_progress.completion_date
                            )}
                          </p>
                        )}
                      {activeTab === 'IN-PROGRESS' &&
                        'completed_activities' in user_progress && (
                          <p className="text-ctx-content-base ts-heading-xxs">
                            {user_progress.completed_activities}/
                            {user_progress.total_activities} aulas visualizadas
                          </p>
                        )}
                    </p>
                  </div>
                ))}
            </div>
          </div>
        </ScrollArea>
      </DrawerContent>
    </Drawer>
  )
}
