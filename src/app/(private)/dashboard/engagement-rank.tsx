import { useState } from 'react'
import { useQuery } from 'react-query'

import { SkeletonEngagementRank } from '@/components/loaders/skeletons/engagement-rank'
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'
import { SelectAds } from '@/components/ui/select-ads'
import { orderByFilters, OrderByTypes } from '@/consts/dashboard'
import { useAuth } from '@/contexts/AuthContext'
import { getEngagementRank } from '@/http/dashboard/get-engagement-rank'

import { EmptyChart } from './empty-chart'
import { EngagementDetails } from './engagement-details'

interface EngagementRankProps {
  start_date?: Date
  end_date?: Date
}

export function EngagementRank({ start_date, end_date }: EngagementRankProps) {
  const { user } = useAuth()

  const [orderBy, setOrderBy] = useState<OrderByTypes>('CLASSES')

  const { data, isLoading, isFetching } = useQuery({
    queryKey: ['getEngagementRank', start_date, end_date],
    queryFn: () =>
      getEngagementRank({
        start_date,
        end_date,
        company_id: Number(user?.metadata.company_id),
      }),
    enabled: !!end_date && !!user?.metadata.company_id,
    refetchOnWindowFocus: false,
    keepPreviousData: true,
  })

  const dashboard = {
    CLASSES: data?.b2bStatistics.usersEngagementRanking,
    COURSES: data?.b2bStatistics.usersCourseCompletedRanking,
  }[orderBy]

  const isLoadingData = isLoading || isFetching || !user?.metadata.company_id
  const isEmpty = !isLoadingData && (!dashboard || dashboard.length === 0)

  return (
    <>
      <div className="mb-10 flex flex-col justify-between gap-4 sm:flex-row">
        <div>
          <h3 className="text-ctx-content-title ts-heading-md">
            Ranking de Engajamento
          </h3>
          <p className="mt-1 text-ctx-content-base ts-heading-xxs">
            Analise os usuários mais ativos e engajados
          </p>
        </div>
        <div className="w-full sm:max-w-[280px]">
          <SelectAds
            size="md"
            fullWidth
            label="Status"
            options={orderByFilters}
            placeholder="Ordernar por"
            value={orderBy}
            onValueChange={(e: OrderByTypes) => setOrderBy(e)}
            custom={{ content: 'z-40 ' }}
            className="[&>button]:bg-ctx-interactive-secondary"
          />
        </div>
      </div>

      {isLoadingData && <SkeletonEngagementRank />}

      {isEmpty && (
        <EmptyChart
          title="Nenhum dado de engajamento neste período"
          description="Altere o intervalo de datas para visualizar outros resultados ou acompanhe quando os colaboradores finalizarem as aulas ou cursos."
        />
      )}

      {!isLoadingData && !isEmpty && (
        <ScrollArea type="auto" className="w-full pb-5">
          <div className="min-w-[700px]">
            <table className="w-full table-fixed border-collapse">
              <thead>
                <tr className="text-left text-ctx-content-title ts-subtitle-xs">
                  <th className="truncate pl-7 font-medium">Usuários</th>
                  <th className="truncate font-medium">Equipe</th>
                  <th className="truncate font-medium">Cursos Finalizados</th>
                  <th className="truncate font-medium">Aulas Finalizadas</th>
                </tr>
              </thead>

              {/* Linhas */}
              <tbody>
                {dashboard?.map(
                  ({ user, activities_completed, courses_completed }, idx) => (
                    <tr
                      key={user.id}
                      className="text-ctx-content-title ts-paragraph-xs last:border-none"
                    >
                      <td className="flex items-center py-2 pr-7">
                        #{idx + 1}
                        <p className="ml-3 truncate">{user.name}</p>
                      </td>
                      <td className="truncate py-2 pr-7">
                        {user.metadata?.company_squad?.title || '-'}
                      </td>
                      <td className="flex items-center gap-1 py-2 pr-7">
                        {courses_completed}
                        <EngagementDetails user={user} />
                      </td>
                      <td className="py-2 pr-7">{activities_completed}</td>
                    </tr>
                  )
                )}
              </tbody>
            </table>
          </div>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      )}
    </>
  )
}
