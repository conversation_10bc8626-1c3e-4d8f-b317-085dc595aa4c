'use client'
import { IconShape } from '@ads/components-react'
import { Search } from 'lucide-react'

interface EmptyChartProps {
  title?: string
  description?: string
}

export function EmptyChart({
  title = 'Nenhum curso acessado ainda',
  description = 'Altere o intervalo de datas para visualizar outros resultados ou acompanhe quando os colaboradores começarem a acessar novos cursos.',
}: EmptyChartProps) {
  return (
    <div className="mx-auto flex min-h-[400px] w-full flex-col items-center justify-center gap-4 rounded-2xl">
      <IconShape
        className="bg-ctx-highlight-promote"
        type="brand"
        size="md"
        icon={() => <Search size={28} className="text-base-gray-900" />}
      />
      <div>
        <h4 className="text-center text-ctx-content-title ts-heading-xs">
          {title}
        </h4>
        <p className="mt-1 text-center text-ctx-content-base ts-paragraph-xxs">
          {description}
        </p>
      </div>
    </div>
  )
}
