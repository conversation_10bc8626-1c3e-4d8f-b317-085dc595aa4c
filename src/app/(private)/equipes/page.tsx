'use client'

import { Search } from '@ads/components-react'
import { Button } from '@ads/components-react'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { useQuery } from 'react-query'

import { PageLayout } from '@/components/layouts/page-layout'
import { TeamsTable } from '@/components/tables/teams'
import { useAuth } from '@/contexts/AuthContext'
import { useDebouncedValue } from '@/hooks/use-debounce'
import { getTeams } from '@/http/teams/get-teams'

import { EmptyList } from './empty-list'

export default function Teams() {
  const { user } = useAuth()

  const [currentPage, setCurrentPage] = useState(1)
  const [searchValue, setSearchValue] = useState<string>()

  const debouncedSearchValue = useDebouncedValue(searchValue, 500)

  const {
    data: teams,
    isFetching,
    isLoading,
  } = useQuery({
    queryKey: [
      'getTeamsGql',
      debouncedSearchValue,
      currentPage,
      user?.metadata.company_id,
    ],
    queryFn: () =>
      getTeams({
        title: debouncedSearchValue ?? '',
        company_id: user?.metadata.company_id ?? 0,
        page: currentPage,
        limit: 10,
      }),
    keepPreviousData: true,
    staleTime: 60 * 1000 * 10,
  })

  const isLoadingTable = currentPage === 1 && (isLoading || isFetching)
  const isFetchingTable = currentPage !== 1 && (isLoading || isFetching)

  useEffect(() => {
    setCurrentPage(1)
  }, [debouncedSearchValue, setCurrentPage])

  return (
    <PageLayout
      title="Equipes"
      description="Gerencie, credencie e edite as equipes"
    >
      <div className="flex flex-col-reverse items-center justify-between gap-5 lg:flex-row">
        <div className="w-full lg:max-w-[559px] [&_button.at-rounded-component-iconButton-border-radius]:hidden">
          <Search
            size="md"
            placeholder="Buscar por equipe"
            className="bg-ctx-interactive-secondary [&_button.at-rounded-component-iconButton-border-radius]:hidden"
            handleChange={(e) => setSearchValue(e)}
            query={searchValue}
          />
        </div>
        <Link href="/equipes/novo" className="w-full lg:w-auto">
          <Button size="md" className="w-full lg:w-auto">
            Criar nova Equipe
          </Button>
        </Link>
      </div>

      <div className="mt-5 md:mt-8">
        {!isLoading && !isFetching && !teams?.companySquads.data.length ? (
          <EmptyList />
        ) : (
          <TeamsTable
            isLoading={isLoadingTable}
            isFetchingNewPage={isFetchingTable}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            teams={teams?.companySquads ?? { data: [], total: 0, perPage: 0 }}
          />
        )}
      </div>
    </PageLayout>
  )
}
