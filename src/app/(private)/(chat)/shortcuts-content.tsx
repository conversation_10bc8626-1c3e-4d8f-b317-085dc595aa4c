'use client'

import { Button } from '@ads/components-react'
import { AnimatePresence, motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import {
  HiChevronDown,
  HiChevronUp,
  HiOutlineAcademicCap,
  HiOutlineLightBulb,
  HiOutlineSquares2X2,
  HiOutlineUser,
  HiOutlineUserGroup,
} from 'react-icons/hi2'

import { ShortcutCard } from '@/components/ui/cards/shortcut'
import { cn } from '@/lib/utils'

interface Shortcut {
  title: string
  description: string
  Icon: React.ComponentType
  type?: 'default' | 'shortly'
  url: string
}

const shortcuts: Shortcut[] = [
  {
    title: 'Dashboard',
    description:
      'Acompanhe os dados, progresso nos cursos e desenvolvimento do time em tempo real.',
    Icon: HiOutlineSquares2X2,
    url: '/dashboard',
  },
  {
    title: 'Planejar Desenvolvimento',
    description:
      'Escreva um desafio ou suba um PDI. A IA analisa, sugere cursos e você atribui em poucos cliques.',
    Icon: HiOutlineLightBulb,
    url: '/trilhas-de-aprendizagem',
  },
  {
    title: 'Gestão de Usuários',
    description:
      'Gerencie colaboradores da sua organização e acesse o histórico de desenvolvimento.',
    Icon: HiOutlineUser,
    url: '/usuarios',
  },
  {
    title: 'Gestão de Equipes',
    description:
      'Organize os colaboradores, planeje trilhas de desenvolvimento por grupo.',
    Icon: HiOutlineUserGroup,
    url: '/equipes',
  },
  {
    title: 'Treinar e Desenvolver',
    description:
      'Crie e edite trilhas, atribua cursos, acompanhe o desempenho e visualize o impacto.',
    Icon: HiOutlineAcademicCap,
    type: 'shortly',
    url: '/trilhas-de-aprendizagem',
  },
]

export function ShortcutContent() {
  const [expanded, setExpanded] = useState(false)
  const router = useRouter()

  const handleShortcutClick = (url: string) => {
    router.push(url)
  }

  const visibleShortcuts = expanded ? shortcuts : shortcuts.slice(0, 3)
  return (
    <div>
      <div
        className={cn(
          'mx-auto grid w-fit max-w-[700px] grid-cols-1 gap-4 transition-all duration-300 lg:grid-cols-3 lg:gap-5'
        )}
      >
        <AnimatePresence initial={false}>
          {visibleShortcuts.map(({ type, ...shortcut }, index) => (
            <motion.div
              key={`shortcut-${index}-${shortcut.title}`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.2, delay: index * 0.1 }}
              layout={false}
            >
              <ShortcutCard
                data={shortcut}
                type={type}
                onClick={() => handleShortcutClick(shortcut.url)}
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 20 }}
        transition={{ duration: 0.3 }}
        className="mt-2 flex justify-center lg:justify-end"
      >
        <Button
          hierarchy="tertiary"
          size="sm"
          trailingIcon={expanded ? HiChevronUp : HiChevronDown}
          onClick={() => setExpanded(!expanded)}
        >
          {expanded ? 'Ver menos' : 'Ver mais'}
        </Button>
      </motion.div>
    </div>
  )
}
