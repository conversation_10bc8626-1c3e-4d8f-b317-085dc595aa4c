'use client'

import { IconShape, useAlert } from '@ads/components-react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { HiOutlineSparkles } from 'react-icons/hi2'
import { useMutation } from 'react-query'

import { InputChat } from '@/components/ui/input-chat'
import { Skeleton } from '@/components/ui/skeleton'
import { useAuth } from '@/contexts/AuthContext'
import { postChatMessageAI } from '@/http/chat-ai/post-chat-ai'
import { type ChatSchema, chatSchema } from '@/validations/chat'

const loadingMessages = [
  'Analisando seu desafio...',
  'Buscando os melhores cursos para você...',
  'Organizando o conteúdo ideal...',
]

export function ChatForm() {
  const { push } = useRouter()
  const { alert } = useAlert()
  const { user, isLoading } = useAuth()

  const [currentMessageIndex, setCurrentMessageIndex] = useState(0)

  const { register, handleSubmit, watch } = useForm<ChatSchema>({
    resolver: zodResolver(chatSchema),
    defaultValues: {
      context: '',
    },
  })

  const { mutate, isLoading: isMutating } = useMutation({
    mutationFn: (data: ChatSchema) => postChatMessageAI(data),
    onSuccess: (response) => {
      push(`/assistente-ia/${response.chat_id}`)
    },
    onError: () => {
      alert({
        title: 'Erro ao criar trilha de aprendizado',
        description:
          'Ocorreu um erro ao criar a trilha de aprendizado. Tente novamente mais tarde.',
        alertType: 'danger',
      })
    },
  })

  const contextValue = watch('context')

  function onSubmit(data: ChatSchema) {
    setCurrentMessageIndex(0)
    mutate(data)
  }

  function handleKeyDown(
    e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(onSubmit)()
    }
  }

  useEffect(() => {
    if (!isMutating) return
    const interval = setInterval(() => {
      setCurrentMessageIndex((prev) => (prev + 1) % loadingMessages.length)
    }, 5000)
    return () => clearInterval(interval)
  }, [isMutating])

  return (
    <form className="flex h-[calc(100vh-64px)] w-full flex-col justify-center">
      <div className="flex w-full flex-col items-center justify-center">
        <IconShape
          className="bg-ctx-highlight-promote text-base-gray-900"
          type="brand"
          size="md"
          icon={() => <HiOutlineSparkles size={26} />}
        />

        {isLoading ? (
          <Skeleton className="mt-8 h-8 w-1/3" />
        ) : (
          <h1 className="mt-8 text-center text-ctx-content-title ts-heading-sm md:ts-heading-md">
            Olá, {user?.name}! <br /> Descubra conteúdos para desenvolver sua
            equipe
          </h1>
        )}

        <p className="text-ctx-content-text mt-4 text-center text-ctx-content-base ts-paragraph-xxs md:ts-paragraph-xs">
          Encontre sugestões personalizadas e atribua aos colaboradores
        </p>
      </div>

      <div className="mx-auto mt-8 flex w-full max-w-[800px] flex-col items-center justify-center space-y-6">
        <InputChat
          disabled={isMutating}
          placeholder="Digite um tema, habilidade ou necessidade da sua equipe. Ex.: vendas, gestão de projetos, comunicação."
          onKeyDown={handleKeyDown}
          onClickSendMessage={() => handleSubmit(onSubmit)()}
          disabledButtonMessage={contextValue?.length <= 0 || isMutating}
          {...register('context')}
        />

        {isMutating && (
          <div className="flex flex-col items-center space-y-2">
            <div className="flex items-center space-x-2">
              <div className="h-2 w-2 animate-bounce rounded-full bg-ctx-highlight-promote [animation-delay:-0.3s]"></div>
              <div className="h-2 w-2 animate-bounce rounded-full bg-ctx-highlight-promote [animation-delay:-0.15s]"></div>
              <div className="h-2 w-2 animate-bounce rounded-full bg-ctx-highlight-promote"></div>
            </div>

            <p className="text-ctx-content-subtitle text-center transition-all duration-300 ease-in-out">
              {loadingMessages[currentMessageIndex]}
            </p>
          </div>
        )}
      </div>

      <p className="mt-8 text-center text-ctx-content-base ts-heading-xxs">
        Quanto mais específico for, melhores serão as recomendações.
      </p>
    </form>
  )
}
