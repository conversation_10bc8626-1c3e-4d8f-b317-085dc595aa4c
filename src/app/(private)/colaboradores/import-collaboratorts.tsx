import { But<PERSON>, useAlert } from '@ads/components-react'
import axios from 'axios'
import { Download, Trash2, Upload } from 'lucide-react'
import { useCallback, useMemo, useRef, useState } from 'react'
import { useMutation } from 'react-query'
import Cookies from 'universal-cookie'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import {
  ErrorCodeUploadColLaborators,
  uploadColaborators,
} from '@/http/collaborators/upload-collaborators'
import { queryClient } from '@/lib/queryClient'
import { cn } from '@/lib/utils'

export function ImportCollaborators() {
  const { alert } = useAlert()

  const [file, setFiles] = useState<File>()
  const [isDragging, setIsDragging] = useState(false)

  const fileInputRef = useRef<HTMLInputElement>(null)

  const enrollmentId = useMemo(() => {
    const cookies = new Cookies(null, { path: '/' })
    return String(cookies.get('b2bEnrollmentId'))
  }, [])

  const { mutate: onUpload, isLoading } = useMutation({
    mutationFn: () => uploadColaborators(file!, enrollmentId),
    onSuccess: () => {
      queryClient.refetchQueries(['getCollaboratorsGql'], {
        active: true,
        exact: false,
      })
      alert({
        title: 'Importação concluída',
        description: 'Os colaboradores foram importados com sucesso.',
        alertType: 'success',
      })
    },
    onError: (error) => {
      const dispatchAlert = (description: string) =>
        alert({
          title: 'Erro ao importar colaboradores',
          description,
          alertType: 'danger',
        })

      if (!axios.isAxiosError(error) || !error.response?.data) {
        dispatchAlert('Ocorreu um erro inesperado')
        return
      }

      const { data } = error.response

      if (data?.message === 'Exceeding CSV length limit') {
        dispatchAlert(
          'O arquivo selecionado ultrapassa o limite de 10.000 usuários.'
        )
        return
      }

      const code = data.code as keyof typeof ErrorCodeUploadColLaborators

      if (code in ErrorCodeUploadColLaborators) {
        dispatchAlert(ErrorCodeUploadColLaborators[code])
        return
      }

      dispatchAlert('Ocorreu um erro inesperado')
    },
  })

  const handleFiles = useCallback((fileList: FileList) => {
    const file = fileList[0]

    if (!file.name.toLowerCase().endsWith('.csv')) {
      return alert({
        title: 'Erro ao importar arquivos',
        description: 'Por favor, selecione apenas arquivos CSV.',
        alertType: 'information',
      })
    }

    setFiles(file)
  }, [])

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(false)
    if (e.dataTransfer.files.length) {
      handleFiles(e.dataTransfer.files)
    }
  }

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = () => setIsDragging(false)

  const handleRemoveFile = () => {
    setFiles(undefined)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleDownloadTemplate = () => {
    const link = document.createElement('a')
    link.href = '/files/template-importar-colaboradores.csv'
    link.download = 'template-importar-colaboradores.csv'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const handleUpload = () => {
    onUpload()
    setFiles(undefined)
  }

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button
          size="md"
          hierarchy="secondary"
          className="flex w-full items-center lg:w-auto"
          leadingIcon={Upload}
        >
          Importar
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent className="max-w-xl">
        <AlertDialogHeader>
          <AlertDialogTitle className="text-ctx-content-title ts-heading-sm">
            Envie um arquivo CSV com os dados dos seus Colaboradores.
          </AlertDialogTitle>
          <AlertDialogDescription className="text-ctx-content-base ts-paragraph-xxs">
            A lista de colaboradores será atualizada.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <div
          className={cn(
            'mt-4 rounded-lg py-4 text-center transition-all',
            isDragging && 'border border-dashed border-ctx-interactive-primary'
          )}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept=".csv"
            className="hidden"
            onChange={(e) => e.target.files && handleFiles(e.target.files)}
          />
          <p className="text-ctx-content-base ts-paragraph-xs">
            Solte o arquivo CSV aqui. <br /> O limite para upload de arquivos
            CSV é de 10.000 usuários por arquivo.
          </p>
        </div>
        <div className="flex flex-col items-center justify-center gap-3 sm:flex-row">
          <Button
            size="sm"
            hierarchy="tertiary"
            leadingIcon={Download}
            onClick={handleDownloadTemplate}
          >
            Baixar template em Excel
          </Button>
          <Button
            size="sm"
            hierarchy="tertiary"
            leadingIcon={Upload}
            onClick={() => fileInputRef.current?.click()}
          >
            Importar arquivo CSV
          </Button>
        </div>
        {file && (
          <div className="mt-4 flex flex-col gap-2 rounded-md bg-ctx-layout-surface p-4">
            <div className="flex items-center justify-between">
              <span className="text-ctx-content-base ts-paragraph-xs">
                {file.name}
              </span>
              <Button size="sm" hierarchy="tertiary" onClick={handleRemoveFile}>
                <Trash2 size={16} />
              </Button>
            </div>
          </div>
        )}
        <AlertDialogFooter className="mt-6 gap-y-3">
          <AlertDialogCancel asChild>
            <Button
              hierarchy="secondary"
              size="lg"
              className="w-full sm:w-fit"
              onClick={() => setFiles(undefined)}
            >
              Cancelar
            </Button>
          </AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button
              size="lg"
              className="w-full sm:w-fit"
              disabled={!file}
              isLoading={isLoading}
              onClick={handleUpload}
            >
              Finalizar importação
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
