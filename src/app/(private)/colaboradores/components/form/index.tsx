'use client'

import { But<PERSON>, useAlert } from '@ads/components-react'
import { zodResolver } from '@hookform/resolvers/zod'
import { format } from 'date-fns'
import { User } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { useMutation, useQueryClient } from 'react-query'

import { CancelCreateCollaboratorAlert } from '@/components/alerts-dialogs/cancel-create-collaborator'
import { CollaboratorsFormSkeleton } from '@/components/loaders/skeletons/collaboratorsForm'
import { InputField } from '@/components/ui/input-form'
import { Combobox } from '@/components/ui/multiselect-combobox'
import { SelectAds } from '@/components/ui/select-ads'
import { Separator } from '@/components/ui/separator'
import {
  allPositions,
  allSeniorities,
  collaboratorTypes,
} from '@/consts/collaborator'
import { EPosition } from '@/enum/position'
import { ESeniority } from '@/enum/seniority'
import { GetGroupsQuery, GetTeamsQuery } from '@/graphql/generated/graphql'
import { createUser } from '@/http/collaborators/create-collaborator'
import { editCollaborator } from '@/http/collaborators/update-collaborator'
import { Avatar } from '@/model/avatar'
import { LabelAndValue } from '@/model/labelAndValue'
import { IUser } from '@/model/user'
import { useCompanyStore } from '@/store/useCompanyStore'

import { AvatarSelection } from '../avatar'
import { ProfileFormData, profileSchema } from './validations'

type CollaboratorFormProps = {
  isEditMode?: boolean
  defaultValues?: IUser
  isLoadingRequests?: boolean
  allTeams?: GetTeamsQuery
  allGroups?: GetGroupsQuery
}

export function CollaboratorForm({
  isEditMode,
  defaultValues,
  isLoadingRequests,
  allTeams,
  allGroups,
}: CollaboratorFormProps) {
  const queryClient = useQueryClient()
  const router = useRouter()
  const { alert } = useAlert()
  const { currentEnrollmentId, companyId } = useCompanyStore()

  const [avatarSelected, setAvatarSelected] = useState<Avatar | null>(null)

  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: defaultValues && {
      name: defaultValues?.name,
      email: defaultValues?.email,
      collaboratorType:
        defaultValues?.roles?.[0]?.name.toLowerCase() === 'b2b_student'
          ? 'student'
          : 'admin',
      team: String(defaultValues?.metadata?.squad_id || ''),
      groups: [] as LabelAndValue[],
      position: defaultValues?.position || '',
      seniority: defaultValues?.seniority || '',
      birthdate: defaultValues?.birthdate
        ? format(new Date(defaultValues?.birthdate), 'yyyy-MM-dd')
        : '',
      admitedAt: defaultValues?.admitted_at
        ? format(new Date(defaultValues?.admitted_at), 'yyyy-MM-dd')
        : '',
    },
  })

  const {
    mutate: createCollaboratorMutation,
    isLoading: isCreatingCollaborator,
  } = useMutation({
    mutationFn: (data: ProfileFormData) =>
      createUser({
        data: {
          name: data.name,
          email: data.email,
          role_ids: [data.collaboratorType === 'student' ? 4 : 5],
          company_id: companyId,
          enrollment_id: Number(currentEnrollmentId),
          redirect_url:
            data.collaboratorType === 'student'
              ? String(process.env.NEXT_PUBLIC_REDIRECT_URL_LMS)
              : String(process.env.NEXT_PUBLIC_REDIRECT_URL_B2B),
          squad_id: Number(data.team),
          avatar: avatarSelected ?? undefined,
          groups_ids: data.groups?.map((group) => Number(group.value)) || [],
          position: data.position ? (data.position as EPosition) : null,
          seniority: data.seniority ? (data.seniority as ESeniority) : null,
          birthdate: data.birthdate,
          admitted_at: data.admitedAt,
        },
      }),
    onSuccess: () => {
      queryClient.invalidateQueries(['getCollaboratorsGql'])
      alert({
        title: 'Colaborador criado com sucesso',
        description: 'O colaborador foi criado com sucesso.',
        alertType: 'success',
      })
      router.push('/colaboradores')
    },
    onError: () => {
      alert({
        title: 'Erro ao criar colaborador',
        description: 'Ocorreu um erro ao criar o colaborador. Tente novamente.',
        alertType: 'danger',
      })
    },
  })

  const {
    mutate: updateCollaboratorMutation,
    isLoading: isUpdatingCollaborator,
  } = useMutation({
    mutationFn: (data: ProfileFormData) =>
      editCollaborator({
        userId: Number(defaultValues?.id),
        data: {
          name: data.name,
          email: data.email,
          role_ids: [data.collaboratorType === 'student' ? 4 : 5],
          company_id: companyId,
          enrollment_id: Number(currentEnrollmentId),
          redirect_url:
            data.collaboratorType === 'student'
              ? String(process.env.NEXT_PUBLIC_REDIRECT_URL_LMS)
              : String(process.env.NEXT_PUBLIC_REDIRECT_URL_B2B),
          squad_id: Number(data.team),
          avatar: avatarSelected ?? undefined,
          groups_ids: data.groups?.map((group) => Number(group.value)) || [],
          position: data.position ? (data.position as EPosition) : null,
          seniority: data.seniority ? (data.seniority as ESeniority) : null,
          birthdate: data.birthdate || undefined,
          admitted_at: data.admitedAt || undefined,
        },
      }),
    onSuccess: () => {
      queryClient.invalidateQueries(['getCollaboratorsGql'])
      alert({
        title: 'Colaborador atualizado com sucesso',
        description: 'O colaborador foi atualizado com sucesso.',
        alertType: 'success',
      })
      router.push('/colaboradores')
    },
    onError: () => {
      alert({
        title: 'Erro ao atualizar colaborador',
        description:
          'Ocorreu um erro ao atualizar o colaborador. Tente novamente.',
        alertType: 'danger',
      })
    },
  })

  const onSubmit = async (data: ProfileFormData) => {
    if (isEditMode) {
      return updateCollaboratorMutation(data)
    }
    createCollaboratorMutation(data)
  }

  useEffect(() => {
    if (defaultValues?.avatar) {
      setAvatarSelected(defaultValues.avatar)
    }
  }, [defaultValues])

  const handleCancel = () => {
    router.push('/colaboradores')
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="space-y-8 rounded-md bg-ctx-layout-body px-4 py-6 md:px-6 md:py-10">
        {isLoadingRequests ? (
          <CollaboratorsFormSkeleton />
        ) : (
          <>
            <AvatarSelection
              avatarSelected={avatarSelected}
              setAvatarSelected={setAvatarSelected}
            />

            <section className="space-y-9">
              <h2 className="flex gap-2 text-ctx-content-title ts-heading-sm">
                <User /> Dados do colaborador
              </h2>

              <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
                <InputField
                  id="nome"
                  label="Nome"
                  type="text"
                  placeholder="Digite o nome completo"
                  errorMessage={errors.name?.message}
                  {...register('name')}
                />

                <InputField
                  id="email"
                  label="Email"
                  type="email"
                  placeholder="Digite o email"
                  errorMessage={errors.email?.message}
                  {...register('email')}
                />

                <Controller
                  name="team"
                  control={control}
                  render={({ field }) => (
                    <SelectAds
                      label="Equipe (Opcional)"
                      placeholder="Selecione uma equipe"
                      disabled={
                        isLoadingRequests ||
                        allTeams?.companySquads.data.length === 0
                      }
                      options={
                        allTeams?.companySquads.data.map(({ id, title }) => ({
                          value: String(id),
                          label: title,
                        })) || []
                      }
                      onValueChange={field.onChange}
                      value={field.value || ''}
                    />
                  )}
                />

                <Controller
                  name="groups"
                  control={control}
                  render={({ field }) => (
                    <Combobox
                      label="Grupos (Opcional)"
                      placeholder="Selecione um ou mais Grupos para este Colaborador"
                      disabled={
                        isLoadingRequests ||
                        allGroups?.companyGroups.data.length === 0
                      }
                      options={
                        allGroups?.companyGroups.data.map(({ id, name }) => ({
                          value: String(id),
                          label: name,
                        })) || []
                      }
                      onChange={field.onChange}
                      value={field.value}
                      errorMessage={errors.groups?.message}
                    />
                  )}
                />

                <Controller
                  name="collaboratorType"
                  control={control}
                  render={({ field }) => (
                    <SelectAds
                      label="Tipo de colaborador"
                      placeholder="Selecione o tipo"
                      options={collaboratorTypes}
                      onValueChange={field.onChange}
                      value={field.value}
                      errorMessage={errors.collaboratorType?.message}
                    />
                  )}
                />
              </div>

              <Separator />

              <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
                <Controller
                  name="position"
                  control={control}
                  render={({ field }) => (
                    <SelectAds
                      label="Cargo (Opcional)"
                      placeholder="Selecione um cargo"
                      options={allPositions}
                      onValueChange={field.onChange}
                      errorMessage={errors.position?.message}
                      value={field.value || ''}
                    />
                  )}
                />

                <Controller
                  name="seniority"
                  control={control}
                  render={({ field }) => (
                    <SelectAds
                      label="Nível (Opcional)"
                      placeholder="Selecione um nível"
                      options={allSeniorities}
                      onValueChange={field.onChange}
                      errorMessage={errors.seniority?.message}
                      value={field.value || ''}
                    />
                  )}
                />

                <InputField
                  type="date"
                  label="Data de Nascimento (Opcional)"
                  placeholder="Selecione a data de nascimento"
                  errorMessage={errors.birthdate?.message}
                  {...register('birthdate')}
                />

                <InputField
                  type="date"
                  label="Data de Admissão (Opcional)"
                  placeholder="Selecione a data de admissão"
                  errorMessage={errors.admitedAt?.message}
                  {...register('admitedAt')}
                />
              </div>

              <div className="flex justify-end gap-4">
                <CancelCreateCollaboratorAlert
                  isEditMode={isEditMode}
                  onConfirm={handleCancel}
                  disabledCancel={
                    isSubmitting ||
                    isCreatingCollaborator ||
                    isUpdatingCollaborator
                  }
                />

                <Button
                  type="submit"
                  isLoading={isCreatingCollaborator || isUpdatingCollaborator}
                  disabled={isSubmitting}
                >
                  Salvar Alterações
                </Button>
              </div>
            </section>
          </>
        )}
      </div>
    </form>
  )
}
