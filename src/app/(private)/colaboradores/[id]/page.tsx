'use client'

import { useParams } from 'next/navigation'
import { useQuery } from 'react-query'

import { PageLayout } from '@/components/layouts/page-layout'
import { useAuth } from '@/contexts/AuthContext'
import { getUserById } from '@/http/get-user-by-id'
import { getGroups } from '@/http/groups'
import { getTeams } from '@/http/teams/get-teams'

import { CollaboratorForm } from '../components/form'

export default function EditarColaborador() {
  const { user } = useAuth()

  const params = useParams()

  const { data, isLoading, isFetching } = useQuery({
    queryKey: ['getUserByIdGql', params.id],
    queryFn: () => getUserById(Number(params.id)),
    refetchOnWindowFocus: false,
  })

  const { data: allTeams, isLoading: isLoadingTeams } = useQuery({
    queryKey: ['getTeams', user],
    queryFn: () =>
      getTeams({
        limit: 999,
        page: 1,
        company_id: user?.metadata?.company_id || 1,
      }),
    refetchOnWindowFocus: false,
  })

  const { data: allGroups, isLoading: isLoadingGroups } = useQuery({
    queryKey: ['getGroups', user],
    queryFn: () =>
      getGroups({
        limit: 999,
        page: 1,
        company_id: user?.metadata?.company_id || 1,
        name: '',
      }),
    refetchOnWindowFocus: false,
  })

  const isGettingUserInfoLoading =
    isLoading || isLoadingTeams || isLoadingGroups || isFetching

  return (
    <PageLayout
      title="Editar Colaborador"
      description="Gerencie os dados do colaborador desta organização"
    >
      <CollaboratorForm
        key={data?.id}
        isEditMode
        defaultValues={data}
        isLoadingRequests={isGettingUserInfoLoading}
        allTeams={allTeams}
        allGroups={allGroups}
      />
    </PageLayout>
  )
}
