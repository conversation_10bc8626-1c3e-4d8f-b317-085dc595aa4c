'use client'

import { alert, But<PERSON>, TextField } from '@ads/components-react'
import axios from 'axios'
import { useRouter } from 'next/navigation'
import { useEffect, useMemo, useState } from 'react'
import { useMutation, useQuery, useQueryClient } from 'react-query'
import Cookies from 'universal-cookie'

import { CancelCreateGroupAlert } from '@/components/alerts-dialogs/cancel-create-group'
import { GroupUsersSelectedTable } from '@/components/tables/group/users-selected'
import { AddUsersModal } from '@/components/ui/groups/modal'
import { createNewGroup } from '@/http/groups'
import { getB2bUsers } from '@/http/user'
import { useSelectedUsersGroups } from '@/store/useSelectedUsersGroup'

import { EmptyList } from '../empty-list'

export default function CreateGroup() {
  const router = useRouter()
  const queryClient = useQueryClient()

  const [groupName, setGroupName] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [error, setError] = useState(false)

  const { companyId, enrollmentId } = (() => {
    const cookies = new Cookies(null, { path: '/' })

    return {
      companyId: cookies.get('b2bCompanyId'),
      enrollmentId: Number(cookies.get('b2bEnrollmentId')),
    }
  })()

  const { selectedAll, selectedUserIds, clearSelected } =
    useSelectedUsersGroups()

  const perPage = 10

  const { mutate: onCreateGroup, isLoading: isLoadingCreate } = useMutation({
    mutationFn: createNewGroup,
    onSuccess: () => {
      queryClient.invalidateQueries(['getGroupsGql'])
      alert({
        alertType: 'success',
        title: 'Grupo criado com sucesso',
        description: `O grupo ${groupName} foi criado com sucesso.`,
      })
      handleBackPage()
    },
    onError: (err) => {
      const description = axios.isAxiosError(err)
        ? err.response?.data.message || 'Erro desconhecido'
        : 'Ocorreu um erro ao criar o grupo. Tente novamente mais tarde.'

      alert({
        alertType: 'danger',
        title: 'Erro ao criar grupo',
        description,
      })
    },
  })

  const { data: usersResponse, isLoading } = useQuery({
    queryKey: ['getb2bUsersSelectedGql', companyId, enrollmentId],
    queryFn: () =>
      getB2bUsers({
        page: 1,
        limit: 9999,
        orderBy: 'NAME',
        company_id: companyId,
        enrollment_id: enrollmentId,
      }),
    keepPreviousData: true,
    cacheTime: 1000 * 60 * 10,
    staleTime: 1000 * 60 * 10,
  })

  const allUsers = usersResponse?.users.data ?? []

  const filteredUsers = useMemo(
    () =>
      allUsers.filter((user) =>
        selectedAll
          ? !selectedUserIds.includes(user.id)
          : selectedUserIds.includes(user.id)
      ),
    [allUsers, selectedAll, selectedUserIds]
  )

  const paginatedUsers = useMemo(
    () =>
      filteredUsers.slice((currentPage - 1) * perPage, currentPage * perPage),
    [filteredUsers, currentPage, perPage]
  )

  const usersByCompanySelected = useMemo(
    () => ({
      data: paginatedUsers,
      total: filteredUsers.length,
      perPage,
    }),
    [paginatedUsers, filteredUsers.length, perPage]
  )

  const hasSomeData =
    selectedAll || Boolean(groupName || selectedUserIds.length)

  const shouldReturnEmptyList =
    !isLoading &&
    ((!selectedAll && !selectedUserIds.length) ||
      (selectedAll && selectedUserIds.length === allUsers.length))

  const handleBackPage = () => {
    clearSelected()
    router.push('/grupos')
  }

  const handleCreateGroup = () => {
    if (!groupName) {
      alert({
        alertType: 'danger',
        title: 'Grupo sem nome',
        description: 'Por favor, informe o nome do grupo.',
      })
      setError(true)
      return
    }

    if (
      (!selectedAll && !selectedUserIds.length) ||
      (selectedAll && selectedUserIds.length === allUsers.length)
    ) {
      alert({
        alertType: 'danger',
        title: 'Grupo sem colaboradores',
        description: 'Selecione ao menos 1 colaborador.',
      })
      return
    }

    onCreateGroup({
      name: groupName,
      company_id: companyId,
      select_all_user_ids: selectedAll,
      user_ids_deselected:
        selectedAll && selectedUserIds.length ? selectedUserIds : null,
      user_ids_selected: !selectedAll ? selectedUserIds : null,
    })
  }

  useEffect(() => {
    const totalPages = Math.ceil(filteredUsers.length / perPage)
    if (currentPage > totalPages && totalPages > 0) {
      setCurrentPage(totalPages)
    }
  }, [filteredUsers.length, currentPage, perPage])

  return (
    <section>
      <div className="rounded-2xl bg-ctx-layout-body p-4 md:p-8">
        <div className="flex flex-col items-end justify-between gap-8 rounded-2xl border border-solid border-ctx-layout-border p-4 md:flex-row">
          <TextField
            label="Nome"
            value={groupName}
            onChange={(e) => setGroupName(e.target.value)}
            hasError={error}
            size="md"
            placeholder="Digite o nome do Grupo"
            fullWidth
            custom={{
              input: 'bg-ctx-interactive-secondary',
            }}
          />

          <AddUsersModal />
        </div>

        <div className="mt-6 w-full">
          {shouldReturnEmptyList ? (
            <EmptyList
              title="Sem Colaborador"
              description="Nenhum colaborador foi atribuido."
            />
          ) : (
            <GroupUsersSelectedTable
              isLoading={isLoading}
              isFetchingNewPage={false}
              currentPage={currentPage}
              setCurrentPage={setCurrentPage}
              usersByCompany={usersByCompanySelected}
            />
          )}
        </div>
      </div>

      <div className="flex justify-end gap-8 py-6">
        <CancelCreateGroupAlert
          onClickCancel={handleBackPage}
          disabledCancel={isLoadingCreate}
          showAlert={hasSomeData}
        />
        <Button
          onClick={handleCreateGroup}
          isLoading={isLoadingCreate}
          disabled={isLoadingCreate}
          size="md"
        >
          Salvar
        </Button>
      </div>
    </section>
  )
}
