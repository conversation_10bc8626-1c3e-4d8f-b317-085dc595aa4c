'use client'

import { But<PERSON>, <PERSON> } from '@ads/components-react'
import Link from 'next/link'
import { useState } from 'react'
import { useQuery } from 'react-query'

import { GroupsTable } from '@/components/tables/group'
import { useAuth } from '@/contexts/AuthContext'
import { useDebouncedValue } from '@/hooks/use-debounce'
import { getGroups } from '@/http/groups'

import { EmptyList } from './empty-list'

export default function Group() {
  const { user } = useAuth()

  const [currentPage, setCurrentPage] = useState(1)
  const [searchValue, setSearchValue] = useState<string>()

  const debouncedSearchValue = useDebouncedValue(searchValue, 500)

  const {
    data: groups,
    isFetching,
    isLoading,
  } = useQuery({
    queryKey: [
      'getGroupsGql',
      debouncedSearchValue,
      currentPage,
      user?.metadata.company_id,
    ],
    queryFn: () =>
      getGroups({
        name: debouncedSearchValue ?? '',
        company_id: user?.metadata.company_id ?? 0,
        page: currentPage,
        limit: 10,
      }),
    enabled: !!user?.metadata.company_id,
    keepPreviousData: true,
    staleTime: 60 * 1000 * 10,
  })

  const isLoadingTable =
    (currentPage === 1 && (isLoading || isFetching)) ||
    !user?.metadata.company_id
  const isFetchingTable = currentPage !== 1 && (isLoading || isFetching)

  return (
    <>
      <div className="flex flex-col-reverse items-center justify-between gap-5 lg:flex-row">
        <div className="w-full lg:max-w-[383px] [&_button.at-rounded-component-iconButton-border-radius]:hidden">
          <Search
            size="lg"
            placeholder="Buscar por grupo"
            className="bg-ctx-interactive-secondary [&_button.at-rounded-component-iconButton-border-radius]:hidden"
            handleChange={(e) => setSearchValue(e)}
            query={searchValue}
          />
        </div>
        <Link href="/grupos/novo" className="w-full lg:w-auto">
          <Button size="md" className="w-full lg:w-auto">
            Criar novo Grupo
          </Button>
        </Link>
      </div>

      <div className="mt-5 md:mt-8">
        {!isLoading &&
        !isFetching &&
        groups?.companyGroups &&
        !groups?.companyGroups.data.length ? (
          <EmptyList />
        ) : (
          <GroupsTable
            isLoading={isLoadingTable}
            isFetchingNewPage={isFetchingTable}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            groups={groups?.companyGroups ?? { data: [], total: 0, perPage: 0 }}
          />
        )}
      </div>
    </>
  )
}
