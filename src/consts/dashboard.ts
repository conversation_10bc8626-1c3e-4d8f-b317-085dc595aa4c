import {
  getEndOfDay,
  getStartOfDay,
  subtractDays,
  subtractYears,
} from '@/utils/convert-date'

// Courses Rank
export type TimePeriodFilterTypes = {
  label: string
  personalized?: boolean
  values: {
    start_date?: Date
    end_date?: Date
  }
}

const today = new Date(Date.now())
const todayStart = getStartOfDay(today)
const todayEnd = getEndOfDay(today)

export const timePeriodFilters: TimePeriodFilterTypes[] = [
  {
    label: 'Tudo',
    values: {
      start_date: getStartOfDay(new Date(0)),
      end_date: todayEnd,
    },
  },
  {
    label: '12 meses',
    values: {
      start_date: getStartOfDay(subtractYears(today, 1)),
      end_date: todayEnd,
    },
  },
  {
    label: '30 dias',
    values: {
      start_date: getStartOfDay(subtractDays(today, 30)),
      end_date: todayEnd,
    },
  },
  {
    label: '7 dias',
    values: {
      start_date: getStartOfDay(subtractDays(today, 7)),
      end_date: todayEnd,
    },
  },
  {
    label: 'Hoje',
    values: {
      start_date: todayStart,
      end_date: todayEnd,
    },
  },
  {
    label: 'Personalizado',
    personalized: true,
    values: {},
  },
]

// Engagement Rank
export type OrderByTypes = 'COURSES' | 'CLASSES'

export type OrderByFilterTypes = { label: string; value: OrderByTypes }

export const orderByFilters: OrderByFilterTypes[] = [
  {
    value: 'COURSES',
    label: 'Cursos finalizados',
  },
  {
    value: 'CLASSES',
    label: 'Aulas finalizadas',
  },
]
