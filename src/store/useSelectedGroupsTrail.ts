import { create } from 'zustand'

type GroupSelected = {
  id: number
  name: string
}

type GroupsStore = {
  groupsSelected: GroupSelected[]
  allGroups: boolean
  excludedGroups: number[]
  toggleGroup: (group: GroupSelected) => void
  isGroupSelecionado: (id: number) => boolean
  setGroupsSelected: (groups: GroupSelected[]) => void
  checkAllGroups: () => void
}

export const useSelectedGroupsTrail = create<GroupsStore>((set, get) => ({
  groupsSelected: [],
  allGroups: false,
  excludedGroups: [],

  toggleGroup: (group) => {
    const { allGroups, excludedGroups, groupsSelected } = get()

    if (allGroups) {
      if (excludedGroups.includes(group.id)) {
        set({
          excludedGroups: excludedGroups.filter((id) => id !== group.id),
        })
      } else {
        set({
          excludedGroups: [...excludedGroups, group.id],
        })
      }
    } else {
      const exists = groupsSelected.some((g) => g.id === group.id)
      if (exists) {
        set({
          groupsSelected: groupsSelected.filter((g) => g.id !== group.id),
        })
      } else {
        set({
          groupsSelected: [...groupsSelected, group],
        })
      }
    }
  },

  isGroupSelecionado: (id) => {
    const { allGroups, excludedGroups, groupsSelected } = get()

    if (allGroups) {
      return !excludedGroups.includes(id)
    }

    return groupsSelected.some((g) => g.id === id)
  },

  setGroupsSelected: (groups) => set({ groupsSelected: groups }),

  checkAllGroups: () =>
    set((state) => ({
      allGroups: !state.allGroups,
      excludedGroups: [],
    })),
}))
