import { create } from 'zustand'
import { persist } from 'zustand/middleware'

type SelectedUsersGroupsStore = {
  selectedUserIds: number[]
  selectedAll: boolean

  tempSelectedUserIds: number[]
  tempSelectedAll: boolean

  toggleTempUserId: (id: number) => void

  setSelectedAll: (status: boolean) => void
  setTempSelectedAll: (status: boolean) => void

  setSelected: (ids: number[]) => void
  setTempSelected: (ids: number[]) => void

  removeSelectedUserId: (id: number) => void
  clearSelected: () => void
}

export const useSelectedUsersGroups = create<SelectedUsersGroupsStore>()(
  persist(
    (set, get) => ({
      selectedUserIds: [],
      selectedAll: false,

      tempSelectedUserIds: [],
      tempSelectedAll: false,

      toggleTempUserId: (id: number) => {
        const current = get().tempSelectedUserIds
        const isSelected = current.includes(id)

        set({
          tempSelectedUserIds: isSelected
            ? current.filter((userId) => userId !== id)
            : [...current, id],
        })
      },

      setSelectedAll: (status) => set({ selectedAll: status }),
      setTempSelectedAll: (status) => set({ tempSelectedAll: status }),

      setSelected: (ids) => set({ selectedUserIds: ids }),
      setTempSelected: (ids) => set({ tempSelectedUserIds: ids }),

      clearSelected: () => {
        set({
          selectedUserIds: [],
          selectedAll: false,
          tempSelectedUserIds: [],
          tempSelectedAll: false,
        })
      },
      removeSelectedUserId: (id) => {
        const { selectedUserIds, selectedAll, tempSelectedUserIds } = get()

        if (selectedAll) {
          set({
            selectedUserIds: [...selectedUserIds, id],
            tempSelectedUserIds: [...tempSelectedUserIds, id],
          })
          return
        }

        set({
          selectedUserIds: selectedUserIds.filter((prevId) => prevId !== id),
          tempSelectedUserIds: tempSelectedUserIds.filter(
            (prevId) => prevId !== id
          ),
        })
      },
    }),
    {
      name: 'selected-users-group-ids',
    }
  )
)
