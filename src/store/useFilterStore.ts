import { create } from 'zustand'

interface FilterState {
  search: string
  status: string
  currentPageKey: string
}

interface FilterActions {
  setCurrentPage: (pageKey: string) => void
  onChangeSearch: (search: string) => void
  onChangeStatus: (status: string) => void
}

const defaultFilterState: Omit<FilterState, 'currentPageKey'> = {
  search: '',
  status: 'ALL',
}

export const useFilterStore = create<FilterState & FilterActions>(
  (set, get) => ({
    ...defaultFilterState,
    currentPageKey: '',

    setCurrentPage: (pageKey: string) => {
      const { currentPageKey } = get()

      if (currentPageKey && currentPageKey !== pageKey) {
        set({
          ...defaultFilterState,
          currentPageKey: pageKey,
        })
      } else {
        set({ currentPageKey: pageKey })
      }
    },

    onChangeSearch: (search: string) => {
      set({ search })
    },

    onChangeStatus: (status: string) => {
      set({ status })
    },
  })
)
