'use client'

import { ADSProvider } from '@ads/components-react'
import { QueryClient, QueryClientProvider } from 'react-query'
import { ReactQueryDevtools } from 'react-query/devtools'

import { AuthProvider } from '@/contexts/AuthContext'

interface ProvidersProps {
  children: React.ReactNode
}

export const Providers = ({ children }: ProvidersProps) => {
  const queryClient = new QueryClient()
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <ADSProvider>
          {children}
          <ReactQueryDevtools initialIsOpen={false} position="bottom-right" />
        </ADSProvider>
      </AuthProvider>
    </QueryClientProvider>
  )
}
