interface CustomCheckboxProps {
  checked: boolean
  onChange: (checked: boolean) => void
}

export function CustomCheckbox({ checked, onChange }: CustomCheckboxProps) {
  return (
    <div className="relative">
      <input
        type="checkbox"
        className="sr-only"
        checked={checked}
        onChange={(e) => onChange(e.target.checked)}
      />
      <div
        className={`flex h-6 w-6 cursor-pointer items-center justify-center rounded-sm border transition-all duration-200 ${
          checked
            ? 'border-ctx-interactive-primary bg-ctx-interactive-primary'
            : 'border-ctx-interactive-secondary bg-ctx-interactive-secondary'
        }`}
        onClick={() => onChange(!checked)}
      >
        {checked && (
          <svg
            className="h-4 w-4 text-black"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={3}
              d="M5 13l4 4L19 7"
            />
          </svg>
        )}
      </div>
    </div>
  )
}
