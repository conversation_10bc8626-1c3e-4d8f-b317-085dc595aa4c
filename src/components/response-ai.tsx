interface ResponseAIProps {
  content?: string
}

export function ResponseAI({ content }: ResponseAIProps) {
  return (
    <div className="flex w-full flex-col space-y-2">
      <h3 className="text-ctx-content-title ts-subtitle-xs">IA:</h3>
      <div className="prose-span:text-ctx-content-base prose min-w-full prose-p:text-ctx-content-base prose-p:ts-paragraph-xs prose-strong:text-ctx-content-base prose-li:text-ctx-content-base">
        <div
          className="min-w-full text-ctx-content-base"
          dangerouslySetInnerHTML={{ __html: content ?? '' }}
        />
      </div>
    </div>
  )
}
