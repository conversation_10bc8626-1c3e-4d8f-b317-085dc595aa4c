'use client'

import React from 'react'

import { cn } from '@/lib/utils'

interface TypingIndicatorProps {
  className?: string
}

export function TypingIndicator({ className }: TypingIndicatorProps) {
  return (
    <div className={cn('flex', className)}>
      <div className="flex gap-1.5 px-4 py-2">
        <div className="h-2.5 w-2.5 animate-[bounce_1s_infinite_-0.3s] rounded-full bg-ctx-highlight-promote opacity-75" />
        <div className="h-2.5 w-2.5 animate-[bounce_1s_infinite_-0.15s] rounded-full bg-ctx-highlight-promote opacity-85" />
        <div className="h-2.5 w-2.5 animate-[bounce_1s_infinite] rounded-full bg-ctx-highlight-promote" />
      </div>
    </div>
  )
}
