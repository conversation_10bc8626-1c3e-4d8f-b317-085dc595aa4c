import { Skeleton } from '@/components/ui/skeleton'

export function SkeletonEngagementRank() {
  return (
    <div className="flex flex-col gap-4">
      <div className="grid grid-cols-2 gap-4 sm:grid-cols-4">
        <Skeleton className="h-8 w-[90%] sm:w-[60%]" />
        <Skeleton className="h-8 w-[90%] sm:w-[60%]" />
        <Skeleton className="hidden h-8 w-[80%] sm:flex" />
        <Skeleton className="hidden h-8 w-[80%] sm:flex" />
      </div>
      {[...Array(2)].map((_, key) => (
        <div key={key} className="grid grid-cols-2 gap-4 sm:grid-cols-4">
          <Skeleton className="h-8 w-[80%] sm:w-[50%]" />
          <Skeleton className="h-8 w-[80%] sm:w-[50%]" />
          <Skeleton className="hidden h-8 w-[70%] sm:flex" />
          <Skeleton className="hidden h-8 w-[70%] sm:flex" />
        </div>
      ))}
    </div>
  )
}
