import { ScrollArea } from '@ads/components-react'

import { Separator } from '@/components/ui/separator'
import { Skeleton } from '@/components/ui/skeleton'

import { ChatSkeleton } from './first-chat'

export function ChatPageSkeleton() {
  return (
    <div className="flex h-[calc(100vh-130px)] flex-col overflow-hidden bg-ctx-layout-body md:h-[calc(100vh-192px)]">
      <ScrollArea className="min-h-0 flex-1">
        <ChatSkeleton />
      </ScrollArea>

      <div className="flex-shrink-0">
        <Separator className="my-2 md:my-4" />
        <div className="mx-auto w-full max-w-[50rem] px-4 pb-4">
          <Skeleton className="h-40 w-full rounded-md" />
        </div>
      </div>
    </div>
  )
}
