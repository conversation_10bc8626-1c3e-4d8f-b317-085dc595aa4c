import {
  Button,
  DropdownItem,
  DropdownItemContent,
} from '@ads/components-react'
import { Trash2 } from 'lucide-react'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'

import { Separator } from '../ui/separator'

type DeleteStudyPlanAlertProps = {
  teamsMutation: () => void
}

export function DeleteTeamsAlert({ teamsMutation }: DeleteStudyPlanAlertProps) {
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <DropdownItem status="destructive" onSelect={(e) => e.preventDefault()}>
          <DropdownItemContent leadingIcon={Trash2}>
            Excluir
          </DropdownItemContent>
        </DropdownItem>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            Tem certeza que deseja excluir Equipe?
          </AlertDialogTitle>
        </AlertDialogHeader>
        <Separator />
        <AlertDialogFooter>
          <AlertDialogAction asChild>
            <Button onClick={teamsMutation} hierarchy="tertiary">
              Excluir
            </Button>
          </AlertDialogAction>
          <AlertDialogCancel asChild>
            <Button>Voltar</Button>
          </AlertDialogCancel>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
