import {
  Button,
  DropdownItem,
  DropdownItemContent,
  IconShape,
} from '@ads/components-react'
import { Info, Trash2 } from 'lucide-react'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'

type DeleteCollaboratorsAlertProps = {
  collaboratorsMutation: () => void
}

export function DeleteCollaboratorsAlert({
  collaboratorsMutation,
}: DeleteCollaboratorsAlertProps) {
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <DropdownItem status="destructive" onSelect={(e) => e.preventDefault()}>
          <DropdownItemContent leadingIcon={Trash2}>
            Excluir Colaborador
          </DropdownItemContent>
        </DropdownItem>
      </AlertDialogTrigger>
      <AlertDialogContent className="gap-6">
        <IconShape
          size="sm"
          icon={Info}
          type="danger"
          className="mx-auto sm:mx-0"
        />
        <AlertDialogHeader>
          <AlertDialogTitle className="text-ctx-content-title ts-heading-md">
            Tem certeza que deseja excluir este Colaborador?
          </AlertDialogTitle>
          <AlertDialogDescription className="text-ctx-content-base ts-paragraph-xs">
            Ao excluir, as todos os dados deste Colaborador serão perdidos e não
            poderão ser recuperados.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="gap-y-3">
          <AlertDialogCancel asChild>
            <Button size="lg" hierarchy="secondary" className="w-full sm:w-fit">
              Cancelar
            </Button>
          </AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button
              size="lg"
              hierarchy="danger"
              className="w-full sm:w-fit"
              onClick={collaboratorsMutation}
            >
              Excluir Colaborador
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
