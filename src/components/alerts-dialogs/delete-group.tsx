import {
  Button,
  DropdownItem,
  DropdownItemContent,
  IconShape,
} from '@ads/components-react'
import { Info, Trash2 } from 'lucide-react'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'

type DeleteGroupAlertProps = {
  groupMutation: () => void
}

export function DeleteGroupAlert({ groupMutation }: DeleteGroupAlertProps) {
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <DropdownItem status="destructive" onSelect={(e) => e.preventDefault()}>
          <DropdownItemContent leadingIcon={Trash2}>
            Excluir
          </DropdownItemContent>
        </DropdownItem>
      </AlertDialogTrigger>
      <AlertDialogContent className="max-w-[700px] gap-6">
        <IconShape
          size="sm"
          icon={Info}
          type="danger"
          className="mx-auto sm:mx-0"
        />
        <AlertDialogHeader>
          <AlertDialogTitle className="text-ctx-content-title ts-heading-md">
            Tem certeza que deseja excluir este Grupo?
          </AlertDialogTitle>
          <AlertDialogDescription className="text-ctx-content-base ts-paragraph-xs">
            Ao excluir, todas as informações do grupo serão perdidas e não
            poderão ser recuperadas.
          </AlertDialogDescription>
        </AlertDialogHeader>

        <AlertDialogFooter className="gap-y-3">
          <AlertDialogCancel asChild>
            <Button size="lg" hierarchy="secondary" className="w-full sm:w-fit">
              Cancelar
            </Button>
          </AlertDialogCancel>

          <AlertDialogAction asChild>
            <Button
              onClick={groupMutation}
              size="lg"
              hierarchy="danger"
              className="w-full sm:w-fit"
            >
              Excluir
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
