import { Tooltip } from '@ads/components-react'
import { ReactNode, useEffect, useState } from 'react'

import { cn } from '@/lib/utils'

type ToggleOption = {
  value: string
  icon?: ReactNode
  tooltip?: string
}

interface ToggleSwitchProps {
  options?: ToggleOption[]
  defaultValue?: string | null
  onChange?: (value: string) => void
  className?: string
}

export function ToggleSwitch({
  options = [],
  defaultValue = null,
  onChange = () => {},
  className = '',
}: ToggleSwitchProps) {
  const [activeValue, setActiveValue] = useState(
    defaultValue || options[0]?.value
  )

  function handleToggle(value: string) {
    setActiveValue(value)
    onChange(value)
  }

  useEffect(() => {
    if (defaultValue && defaultValue !== activeValue) {
      setActiveValue(defaultValue)
    }
  }, [defaultValue, activeValue])

  return (
    <div
      className={`relative flex h-8 w-[72px] items-center justify-center rounded-full border border-ctx-layout-border bg-transparent p-1 ${className} shadow-sm`}
    >
      {options.map((option) => {
        const isActive = option.value === activeValue
        const colorClass = isActive
          ? 'text-ctx-content-title bg-ctx-layout-border'
          : 'text-ctx-content-base'

        const button = (
          <button
            key={option.value}
            onClick={() => handleToggle(option.value)}
            className={cn(
              `relative z-10 flex h-6 w-8 items-center justify-center rounded-full`,
              colorClass
            )}
          >
            {option.icon && option.icon}
          </button>
        )

        return option.tooltip ? (
          <Tooltip key={option.value} side="top" title={option.tooltip}>
            {button}
          </Tooltip>
        ) : (
          button
        )
      })}
    </div>
  )
}
