import { ReactNode, useEffect, useState } from 'react'

import { cn } from '@/lib/utils'

type ToggleOption = {
  value: string
  icon?: ReactNode
}

interface ToggleSwitchProps {
  options?: ToggleOption[]
  defaultValue?: string | null
  onChange?: (value: string) => void
  className?: string
}

export function ToggleSwitch({
  options = [],
  defaultValue = null,
  onChange = () => {},
  className = '',
}: ToggleSwitchProps) {
  const [activeValue, setActiveValue] = useState(
    defaultValue || options[0]?.value
  )

  function handleToggle(value: string) {
    setActiveValue(value)
    onChange(value)
  }

  useEffect(() => {
    if (defaultValue && defaultValue !== activeValue) {
      setActiveValue(defaultValue)
    }
  }, [defaultValue, activeValue])

  return (
    <div
      className={`relative flex h-11 w-[108px] items-center justify-center rounded-full border border-ctx-layout-border bg-ctx-layout-body p-1 ${className} shadow-sm`}
    >
      {options.map((option) => {
        const isActive = option.value === activeValue
        const colorClass = isActive
          ? 'text-ctx-content-title bg-ctx-layout-border'
          : 'text-ctx-content-base'

        return (
          <button
            key={option.value}
            onClick={() => handleToggle(option.value)}
            className={cn(
              `relative z-10 flex h-9 w-12 items-center justify-center rounded-full`,
              colorClass
            )}
          >
            {option.icon && option.icon}
          </button>
        )
      })}
    </div>
  )
}
