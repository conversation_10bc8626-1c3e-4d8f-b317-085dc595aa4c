'use client'

import Link from 'next/link'

import {
  DataTable,
  PaginationInfo,
  TableColumn,
} from '@/components/ui/data-table/data-table'
import { GetTeamsQuery } from '@/graphql/generated/graphql'
import { convertDateToCalendar } from '@/utils/convert-date'

import { TeamDropdownOptions } from './dropdown-options'

type TeamsTableProps = {
  teams: GetTeamsQuery['companySquads']
  isLoading: boolean
  currentPage: number
  setCurrentPage: (page: number) => void
  isFetchingNewPage?: boolean
}

export function TeamsTable({
  teams,
  isLoading,
  currentPage,
  isFetchingNewPage = false,
  setCurrentPage,
}: TeamsTableProps) {
  const paginationInfo: PaginationInfo = {
    currentPage,
    totalPages: Math.ceil(teams.total / teams.perPage) || 1,
    itemsPerPage: teams.perPage,
    totalItems: teams.total,
  }

  const columns: TableColumn<GetTeamsQuery['companySquads']['data'][number]>[] =
    [
      {
        key: 'title',
        header: 'Nome da Equipe',
        accessor: ({ id, title }) => (
          <Link href={`/equipes/${id}`}>{title}</Link>
        ),
      },
      {
        key: 'users_count',
        header: 'Colaboradores',
        accessor: 'users_count',
        headerClassName: 'text-center',
        className: 'text-center',
      },
      {
        key: 'updated_at',
        header: 'Última Atualização',
        accessor: ({ updated_at }) => (
          <p>{convertDateToCalendar(updated_at) || '-'}</p>
        ),
        headerClassName: 'text-center',
        className: 'text-center',
      },
      {
        key: 'options',
        header: '',
        accessor: (team) => <TeamDropdownOptions team={team} />,
      },
    ]

  const handleChangePage = (page: number) => {
    setCurrentPage(page)
  }

  return (
    <DataTable
      data={teams.data}
      columns={columns}
      pagination={paginationInfo}
      onPageChange={handleChangePage}
      loading={isLoading}
      isFetchingNewPage={isFetchingNewPage}
      emptyMessage="Nenhuma equipe encontrada."
    />
  )
}
