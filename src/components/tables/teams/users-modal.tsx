'use client'

import { Checkbox } from '@ads/components-react'

import {
  DataTable,
  PaginationInfo,
  TableColumn,
} from '@/components/ui/data-table/data-table'
import { GetB2BUsersQuery } from '@/graphql/generated/graphql'
import { UsersTeamsType } from '@/http/user'
import { useSelectedUsers } from '@/store/useSelectedUsersTeams'

type StudyPlanTableProps = {
  usersByCompany: GetB2BUsersQuery['users']
  isLoading: boolean
  currentPage: number
  setCurrentPage: (page: number) => void
  isFetchingNewPage?: boolean
}

export function TeamsModalTable({
  usersByCompany,
  isLoading,
  currentPage,
  setCurrentPage,
  isFetchingNewPage = false,
}: StudyPlanTableProps) {
  const { selectedUserIds, toggleUserId } = useSelectedUsers()

  const paginationInfo: PaginationInfo = {
    currentPage,
    totalPages: Math.ceil(usersByCompany.total / usersByCompany.perPage),
    itemsPerPage: usersByCompany.perPage,
    totalItems: usersByCompany.total,
  }

  const columns: TableColumn<UsersTeamsType>[] = [
    {
      key: 'status',
      header: 'Selecionar',
      accessor: (user) => {
        const isDisabled = !!user.metadata?.squad_id
        const isChecked = isDisabled || selectedUserIds.includes(user.id)

        return (
          <div className="flex justify-center pr-6">
            <Checkbox
              onCheckedChange={() => toggleUserId(user.id)}
              disabled={isDisabled}
              checked={isChecked}
              size="md"
              className="dark:rounded-sm dark:bg-[#FFFFFFB8]"
            />
          </div>
        )
      },
      headerClassName: 'text-center',
    },
    {
      key: 'name',
      header: 'Nome do Colaborador',
      accessor: (user) => (
        <>
          <p>{user.name}</p>
          <p> {user.email}</p>
        </>
      ),
    },
    {
      key: 'metadata.company_squad.name.title',
      header: 'Equipe',
      accessor: (user) => user.metadata?.company_squad?.title ?? '-',
      headerClassName: 'text-center',
      className: 'text-center',
    },
  ]

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  return (
    <DataTable
      data={usersByCompany.data}
      columns={columns}
      pagination={paginationInfo}
      onPageChange={handlePageChange}
      loading={isLoading}
      isFetchingNewPage={isFetchingNewPage}
      emptyMessage="Nenhum colaborador encontrado."
      className="p-0"
    />
  )
}
