'use client'

import { Checkbox } from '@ads/components-react'
import { useEffect } from 'react'
import Cookies from 'universal-cookie'

import {
  DataTable,
  PaginationInfo,
  TableColumn,
} from '@/components/ui/data-table/data-table'
import { GetCollaboratorsQuery } from '@/graphql/generated/graphql'
import { CollaboratorData } from '@/model/study-plan'
import { useSelectedCollaboratorsTrail } from '@/store/useSelectedCollaboratorsTrail'

type StudyPlanTableProps = {
  collaborators: GetCollaboratorsQuery['users']
  isLoading: boolean
  currentPage: number
  setCurrentPage: (page: number) => void
  isFetchingNewPage?: boolean
}

export function CollaboratorsTable({
  collaborators,
  isLoading,
  currentPage,
  setCurrentPage,
  isFetchingNewPage = false,
}: StudyPlanTableProps) {
  const paginationInfo: PaginationInfo = {
    currentPage,
    totalPages: Math.ceil(collaborators.total / collaborators.perPage) || 1,
    itemsPerPage: collaborators.perPage,
    totalItems: collaborators.total,
  }

  const toggleCollaborator = useSelectedCollaboratorsTrail(
    (state) => state.toggleCollaborator
  )
  const setCollaboratorsSelected = useSelectedCollaboratorsTrail(
    (state) => state.setCollaboratorsSelected
  )

  const isCollaboratorSelecionado = useSelectedCollaboratorsTrail(
    (state) => state.isCollaboratorSelecionado
  )

  const cookies = new Cookies(null, { path: '/' })

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  useEffect(() => {
    const savedStep3 = cookies.get('step3')
    if (
      savedStep3 &&
      typeof savedStep3 === 'object' &&
      savedStep3.collaborators
    ) {
      const { collaborators } = savedStep3

      setCollaboratorsSelected(collaborators.selected ?? [])

      useSelectedCollaboratorsTrail.setState({
        allCollaborators: collaborators.all ?? false,
        excludedCollaborators: collaborators.excluded ?? [],
      })
    }
  }, [])

  const columns: TableColumn<CollaboratorData>[] = [
    {
      key: 'status',
      header: 'Selecionar',
      accessor: (collaborator) => {
        const collaboratorId = Number(collaborator.id)
        const collaboratorName = String(collaborator.name)

        const isChecked = isCollaboratorSelecionado(collaboratorId)

        return (
          <div className="flex justify-center pr-6">
            <Checkbox
              onCheckedChange={() =>
                toggleCollaborator({
                  id: collaboratorId,
                  name: collaboratorName,
                })
              }
              checked={isChecked}
              size="md"
              className="dark:rounded-sm dark:bg-[#FFFFFFB8]"
            />
          </div>
        )
      },
      headerClassName: 'text-center w-10',
      className: 'text-center',
    },

    {
      key: 'name',
      header: 'Nome do Colaborador',
      accessor: 'name',
      headerClassName: 'text-center',
      className: 'text-center',
    },

    {
      key: 'email',
      header: 'E-mail',
      accessor: 'email',
      headerClassName: 'text-center',
      className: 'text-center',
    },
    {
      key: 'squad',
      header: 'Equipe',
      accessor: (collaborator) => (
        <span>
          <p>
            {collaborator?.metadata?.company_squad?.title
              ? collaborator?.metadata?.company_squad?.title
              : '-'}
          </p>
        </span>
      ),
      headerClassName: 'text-center',
      className: 'text-center',
    },
  ]

  return (
    <DataTable
      data={collaborators.data as CollaboratorData[]}
      columns={columns}
      pagination={paginationInfo}
      onPageChange={handlePageChange}
      loading={isLoading}
      isFetchingNewPage={isFetchingNewPage}
      emptyMessage="Nenhum colaborador encontrado."
      className="overflow-hidden p-0"
    />
  )
}
