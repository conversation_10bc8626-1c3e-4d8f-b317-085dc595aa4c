'use client'

import { Checkbox } from '@ads/components-react'
import { useEffect } from 'react'
import Cookies from 'universal-cookie'

import {
  DataTable,
  PaginationInfo,
  TableColumn,
} from '@/components/ui/data-table/data-table'
import { GetCoursesQuery } from '@/graphql/generated/graphql'
import { CoursesData } from '@/model/study-plan'
import { useSelectedCoursesTrail } from '@/store/useSelectedCoursesTrail'

type StudyPlanTableProps = {
  courses: GetCoursesQuery['courses']
  isLoading: boolean
  currentPage: number
  setCurrentPage: (page: number) => void
  isFetchingNewPage?: boolean
}

export function CoursesTable({
  courses,
  isLoading,
  currentPage,
  setCurrentPage,
  isFetchingNewPage = false,
}: StudyPlanTableProps) {
  const paginationInfo: PaginationInfo = {
    currentPage,
    totalPages: Math.ceil(courses.total / courses.perPage) || 1,
    itemsPerPage: courses.perPage,
    totalItems: courses.total,
  }

  const toggleCourse = useSelectedCoursesTrail((state) => state.toggleCourse)
  const setCoursesSelected = useSelectedCoursesTrail(
    (state) => state.setCoursesSelected
  )
  const coursesSelected = useSelectedCoursesTrail(
    (state) => state.coursesSelected
  )

  const cookies = new Cookies(null, { path: '/' })

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  useEffect(() => {
    const savedCourses = cookies.get('step2')
    if (savedCourses && Array.isArray(savedCourses)) {
      setCoursesSelected(savedCourses)
    }
  }, [])

  const columns: TableColumn<CoursesData>[] = [
    {
      key: 'status',
      header: 'Selecionar',
      accessor: (courses) => {
        const courseId = Number(courses.id)
        const courseTitle = String(courses.title)

        const isChecked = coursesSelected.some((c) => c.id === courseId)

        return (
          <div className="flex justify-center pr-6">
            <Checkbox
              onCheckedChange={(checked) => {
                const isSelected = coursesSelected.some(
                  (c) => c.id === courseId
                )
                if (checked !== isSelected) {
                  toggleCourse({ id: courseId, title: courseTitle })
                }
              }}
              checked={isChecked}
              size="md"
              className="dark:rounded-sm dark:bg-[#FFFFFFB8]"
            />
          </div>
        )
      },
      headerClassName: 'text-center',
      className: 'text-center',
    },

    {
      key: 'title',
      header: 'Nome do Curso',
      accessor: 'title',
      headerClassName: 'text-center',
      className: 'text-center',
    },
    {
      key: 'category',
      header: 'Área do Curso',
      accessor: (course) => (
        <span className="">
          <p>{course.categories.map((cat) => cat.title).join(', ')}</p>
        </span>
      ),
      headerClassName: 'text-center',
      className: 'text-center',
    },
  ]

  return (
    <DataTable
      data={courses.data as CoursesData[]}
      columns={columns}
      pagination={paginationInfo}
      onPageChange={handlePageChange}
      loading={isLoading}
      isFetchingNewPage={isFetchingNewPage}
      emptyMessage="Nenhuma trilha encontrada."
      className="overflow-hidden p-0"
    />
  )
}
