'use client'

import { Tag } from '@ads/components-react'
import Link from 'next/link'

import {
  DataTable,
  PaginationInfo,
  TableColumn,
} from '@/components/ui/data-table/data-table'
import { allPositions, allSeniorities } from '@/consts/collaborator'
import { EPosition } from '@/enum/position'
import { ESeniority } from '@/enum/seniority'
import { GetCollaboratorsQuery } from '@/graphql/generated/graphql'
import { convertDateToCalendar } from '@/utils/convert-date'

import { CollaboratorsDropdownOptions } from './dropdown-options'

type CollaboratorsTableProps = {
  collaborators: GetCollaboratorsQuery['users']
  isLoading: boolean
  currentPage: number
  setCurrentPage: (page: number) => void
  isFetchingNewPage?: boolean
}

export function CollaboratorsTable({
  collaborators,
  isLoading,
  currentPage,
  setCurrentPage,
  isFetchingNewPage = false,
}: CollaboratorsTableProps) {
  const paginationInfo: PaginationInfo = {
    currentPage,
    totalPages: Math.ceil(collaborators.total / collaborators.perPage) || 1,
    itemsPerPage: collaborators.perPage,
    totalItems: collaborators.total,
  }

  const columns: TableColumn<GetCollaboratorsQuery['users']['data'][number]>[] =
    [
      {
        key: 'name',
        header: 'Nome do Colaborador',
        headerClassName: 'whitespace-nowrap truncate',
        className: 'w-full',
        accessor: ({ id, name }) => (
          <Link className="block w-full truncate" href={`/colaboradores/${id}`}>
            {name}
          </Link>
        ),
      },
      {
        key: 'email',
        header: 'E-mail',
        accessor: 'email',
        headerClassName: 'whitespace-nowrap',
        className: 'truncate w-full',
      },
      {
        key: 'type',
        header: 'Tipo de Usuário',
        headerClassName: 'whitespace-nowrap',
        className: 'truncate w-full',
        accessor: ({ roles }) => {
          const rolesFormatted = roles?.map(({ name }) => name.toLowerCase())

          if (rolesFormatted?.some((name) => name.includes('gestor b2b'))) {
            return 'Gestor'
          }

          if (
            rolesFormatted?.some(
              (name) => name.includes('b2b aluno') || name.includes('b2b user')
            )
          ) {
            return 'Aluno'
          }

          return '-'
        },
      },
      {
        key: 'position',
        header: 'Cargo',
        headerClassName: 'whitespace-nowrap',
        className: 'truncate w-full',
        accessor: ({ position }) => {
          const positionMap = Object.fromEntries(
            allPositions.map((item) => [item.value, item.label])
          )

          return positionMap[position as EPosition] || '-'
        },
      },
      {
        key: 'seniority',
        header: 'Nível',
        headerClassName: 'whitespace-nowrap',
        className: 'truncate w-full',
        accessor: ({ seniority }) => {
          const seniorityMap = Object.fromEntries(
            allSeniorities.map((item) => [item.value, item.label])
          )

          return seniorityMap[seniority as ESeniority] || '-'
        },
      },
      {
        key: 'team',
        header: 'Equipe',
        headerClassName: 'whitespace-nowrap',
        className: 'truncate w-full',
        accessor: ({ metadata }) => {
          return metadata?.company_squad?.title || '-'
        },
      },
      {
        key: 'status',
        header: 'Status',
        headerClassName: 'whitespace-nowrap',
        className: 'truncate w-full',
        accessor: ({ enrollments_pivot }) =>
          enrollments_pivot && enrollments_pivot[0]?.status === 'ACTIVE' ? (
            <Tag label="Ativo" status="positive" />
          ) : (
            <Tag label="Inativo" status="negative" />
          ),
      },
      {
        key: 'updated_at',
        header: 'Última Atualização',
        headerClassName: 'whitespace-nowrap text-center',
        className: 'text-center',
        accessor: ({ updated_at }) => (
          <p>{convertDateToCalendar(updated_at) || '-'}</p>
        ),
      },
      {
        key: 'options',
        header: '',
        accessor: (collaborators) => (
          <CollaboratorsDropdownOptions collaborator={collaborators} />
        ),
      },
    ]

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  return (
    <DataTable
      data={collaborators.data}
      columns={columns}
      pagination={paginationInfo}
      onPageChange={handlePageChange}
      loading={isLoading}
      isFetchingNewPage={isFetchingNewPage}
      tableClassName="lg:table-fixed 2xl:table-auto"
      emptyMessage="Nenhum colaborador encontrado."
    />
  )
}
