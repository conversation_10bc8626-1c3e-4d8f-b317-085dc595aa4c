import {
  Dropdown,
  DropdownItem,
  DropdownItem<PERSON>ontent,
  Icon<PERSON>utton,
  useAlert,
} from '@ads/components-react'
import { Edit, EllipsisVertical } from 'lucide-react'
import Link from 'next/link'
import { useMutation, useQueryClient } from 'react-query'

import { DeleteCollaboratorsAlert } from '@/components/alerts-dialogs/delete-collaborators'
import { SendEmailCollaboratorsAlert } from '@/components/alerts-dialogs/send-email-collaborators'
import { Separator } from '@/components/ui/separator'
import { GetCollaboratorsQuery } from '@/graphql/generated/graphql'
import { deleteCollaborator } from '@/http/collaborators/delete-collaborator'
import { sendWellcomeEmail } from '@/http/collaborators/send-wellcome-email'

type CollaboratorsDropdownOptionsProps = {
  collaborator: GetCollaboratorsQuery['users']['data'][number]
}

export function CollaboratorsDropdownOptions({
  collaborator,
}: CollaboratorsDropdownOptionsProps) {
  const queryClient = useQueryClient()
  const { alert } = useAlert()

  const { mutate: onDeleteCollaborator } = useMutation({
    mutationFn: () =>
      deleteCollaborator({
        user_id: collaborator.id,
        enrollment_id: Number(collaborator.enrollments?.[0]?.id),
        has_to_cancel: true,
      }),
    onSuccess: () => {
      queryClient.refetchQueries(['getCollaboratorsGql'], {
        active: true,
        exact: false,
      })
      alert({
        title: 'Colaborador excluído com sucesso!',
        description: `O(A) colaborador(a) ${collaborator.name} foi excluído(a) com sucesso.`,
        alertType: 'success',
      })
    },
    onError: () => {
      alert({
        title: 'Erro ao excluir colaborador',
        description: `Ocorreu um erro ao excluir colaborador(a) ${collaborator.name}. Tente novamente mais tarde.`,
        alertType: 'danger',
      })
    },
  })

  const { mutate: onSendEmail } = useMutation({
    mutationFn: () => sendWellcomeEmail(collaborator.id),
    onSuccess: () => {
      alert({
        title: 'E-mail enviado com sucesso',
        description: 'O e-mail de boas-vindas foi enviado com sucesso',
        alertType: 'success',
      })
    },
    onError: () => {
      alert({
        title: 'Erro ao enviar email',
        description:
          'Ocorreu um erro ao enviar email de boas vindas. Tente novamente mais tarde.',
        alertType: 'danger',
      })
    },
  })

  return (
    <Dropdown
      align="end"
      className="w-[216px]"
      trigger={
        <div className="flex w-full justify-end">
          <IconButton
            ariaLabel="Editar"
            icon={EllipsisVertical}
            hierarchy="tertiary"
            className="!p-0"
          />
        </div>
      }
    >
      <Link href={`/colaboradores/${collaborator.id}`} className="w-full">
        <DropdownItem>
          <DropdownItemContent leadingIcon={Edit}>Editar</DropdownItemContent>
        </DropdownItem>
      </Link>

      <SendEmailCollaboratorsAlert collaboratorsMutation={onSendEmail} />

      <Separator />

      <DeleteCollaboratorsAlert collaboratorsMutation={onDeleteCollaborator} />
    </Dropdown>
  )
}
