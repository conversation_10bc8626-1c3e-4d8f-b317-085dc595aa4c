'use client'

import { Checkbox } from '@ads/components-react'

import {
  DataTable,
  PaginationInfo,
  TableColumn,
} from '@/components/ui/data-table/data-table'
import { GetB2BUsersQuery } from '@/graphql/generated/graphql'
import { UsersTeamsType } from '@/http/user'
import { useSelectedUsersGroups } from '@/store/useSelectedUsersGroup'

type GroupUsersModalTableProps = {
  usersByCompany: GetB2BUsersQuery['users']
  isLoading: boolean
  currentPage: number
  isFetchingNewPage?: boolean
  setCurrentPage: (page: number) => void
}

export function GroupModalTable({
  usersByCompany,
  isLoading,
  currentPage,
  isFetchingNewPage = false,
  setCurrentPage,
}: GroupUsersModalTableProps) {
  const { tempSelectedUserIds, tempSelectedAll, toggleTempUserId } =
    useSelectedUsersGroups()

  const paginationInfo: PaginationInfo = {
    currentPage,
    totalPages: Math.ceil(usersByCompany.total / usersByCompany.perPage) || 1,
    itemsPerPage: usersByCompany.perPage,
    totalItems: usersByCompany.total,
  }

  const columns: TableColumn<UsersTeamsType>[] = [
    {
      key: 'status',
      header: 'Selecionar',
      accessor: (user) => {
        const isChecked = tempSelectedAll
          ? !tempSelectedUserIds.includes(user.id)
          : tempSelectedUserIds.includes(user.id)

        return (
          <div className="flex justify-center pr-6">
            <Checkbox
              onCheckedChange={() => toggleTempUserId(user.id)}
              checked={isChecked}
              className="dark:rounded-sm dark:bg-[#FFFFFFB8]"
              size="md"
            />
          </div>
        )
      },
      headerClassName: 'text-center',
    },
    {
      key: 'name',
      header: 'Nome do Colaborador',
      accessor: (user) => (
        <>
          <p>{user.name}</p>
          <p>{user.email}</p>
        </>
      ),
    },
  ]

  function handlePageChange(page: number) {
    setCurrentPage(page)
  }

  return (
    <DataTable
      data={usersByCompany.data}
      columns={columns}
      pagination={paginationInfo}
      onPageChange={handlePageChange}
      loading={isLoading}
      isFetchingNewPage={isFetchingNewPage}
      emptyMessage="Nenhum colaborador encontrado."
      className="p-0"
    />
  )
}
