'use client'

import { DeleteUserGroupAlert } from '@/components/alerts-dialogs/delete-user-group'
import {
  DataTable,
  PaginationInfo,
  TableColumn,
} from '@/components/ui/data-table/data-table'
import { GetB2BUsersQuery } from '@/graphql/generated/graphql'
import { UsersTeamsType } from '@/http/user'
import { convertDateToCalendar } from '@/utils/convert-date'

type GroupUsersSelectedTableProps = {
  usersByCompany: GetB2BUsersQuery['users']
  isLoading: boolean
  currentPage: number
  isFetchingNewPage?: boolean
  setCurrentPage: (page: number) => void
}

export function GroupUsersSelectedTable({
  usersByCompany,
  isLoading,
  currentPage,
  isFetchingNewPage = false,
  setCurrentPage,
}: GroupUsersSelectedTableProps) {
  const paginationInfo: PaginationInfo = {
    currentPage,
    totalPages: Math.ceil(usersByCompany.total / usersByCompany.perPage),
    itemsPerPage: usersByCompany.perPage,
    totalItems: usersByCompany.total,
  }

  const columns: TableColumn<UsersTeamsType>[] = [
    {
      key: 'name',
      header: 'Nome do Colaborador',
      accessor: (row) => (
        <p>
          {row.name} ({row.email})
        </p>
      ),
    },
    {
      key: 'namesquad',
      header: 'Data de Atribuição',
      accessor: () => convertDateToCalendar(new Date()),
      headerClassName: 'text-center',
      className: 'text-center',
    },
    {
      key: 'status',
      header: '',
      accessor: (row) => (
        <div className="flex justify-end">
          <DeleteUserGroupAlert id={row.id} />
        </div>
      ),
    },
  ]

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  return (
    <DataTable
      data={usersByCompany.data}
      columns={columns}
      pagination={paginationInfo}
      onPageChange={handlePageChange}
      loading={isLoading}
      isFetchingNewPage={isFetchingNewPage}
      emptyMessage="Nenhum colaborador foi atribuido."
      className="p-0"
    />
  )
}
