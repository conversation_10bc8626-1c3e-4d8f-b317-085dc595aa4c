'use client'

import { <PERSON><PERSON>, Dropdown, IconButton, Search } from '@ads/components-react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useEffect, useState } from 'react'
import { Controller, FormProvider, useForm } from 'react-hook-form'
import { CiFilter } from 'react-icons/ci'

import { useDebouncedValue } from '@/hooks/use-debounce'
import { useFilterStore } from '@/store/useFilterStore'

import { DropdownMenu } from './dropdown-menu'
import { type FilterFormValues, filterSchema } from './validations'

interface FilterProps {
  placeholder?: string
  pageKey: string
  onChangeStatus?: (status: string) => void
  onChangeSearch?: (search: string) => void
}

const defaultFormValues: FilterFormValues = {
  search: '',
  status: 'ALL',
}

export function Filter({
  placeholder = 'Busca por Trilha',
  pageKey,
  onChangeSearch,
  onChangeStatus,
}: FilterProps) {
  const { setCurrentPage } = useFilterStore()

  const [open, setOpen] = useState(false)
  const [openMobile, setOpenMobile] = useState(false)
  const [isFilter, setIsFilter] = useState(false)

  const methods = useForm<FilterFormValues>({
    resolver: zodResolver(filterSchema),
    defaultValues: defaultFormValues,
  })

  const { watch, reset, control, handleSubmit, setValue } = methods

  const debouncedSearch = useDebouncedValue(watch('search'), 500)

  const handleSearch = () => {
    onChangeSearch?.(debouncedSearch ?? '')
  }

  const handleClearFilters = () => {
    reset()
    setValue('status', 'ALL')
    onChangeSearch?.('')
    onChangeStatus?.('ALL')
    setIsFilter(false)
    handleClose()
  }

  const handleClose = () => {
    setOpen(false)
    setOpenMobile(false)
  }

  const onSubmit = (values: FilterFormValues) => {
    if (values.status !== 'ALL') {
      setIsFilter(true)
    }
    onChangeStatus?.(values.status ?? 'ALL')
    handleClose()
  }

  useEffect(() => {
    handleSearch()
  }, [debouncedSearch])

  useEffect(() => {
    setCurrentPage(pageKey)
  }, [pageKey])

  const filterButtonClass =
    isFilter && 'border border-ctx-highlight-focus shadow-[0_0_0_4px_#FFF8D4]'

  return (
    <FormProvider {...methods}>
      <div className="flex w-full flex-col space-y-4 xl:px-0">
        <div className="flex space-x-6">
          <div className="w-full md:w-96 [&_button.at-rounded-component-iconButton-border-radius]:hidden">
            <Controller
              name="search"
              control={control}
              render={({ field }) => (
                <Search
                  size="md"
                  placeholder={placeholder}
                  className="bg-ctx-interactive-secondary [&_button.at-rounded-component-iconButton-border-radius]:hidden"
                  handleChange={field.onChange}
                  query={field.value ?? ''}
                />
              )}
            />
          </div>

          <div className="relative">
            <div className="hidden md:flex">
              <Dropdown
                key={pageKey}
                align="end"
                open={open}
                onOpenChange={setOpen}
                trigger={
                  <Button
                    hierarchy="secondary"
                    className={`bg-ctx-interactive-secondary ${filterButtonClass}`}
                    trailingIcon={CiFilter}
                  >
                    Filtros
                  </Button>
                }
              >
                <DropdownMenu
                  onSubmit={handleSubmit(onSubmit)}
                  clearFilters={handleClearFilters}
                />
              </Dropdown>
            </div>

            <div className="md:hidden">
              <Dropdown
                align="end"
                key={pageKey}
                open={openMobile}
                onOpenChange={setOpenMobile}
                trigger={
                  <IconButton
                    ariaLabel="Abrir filtros"
                    hierarchy="secondary"
                    icon={CiFilter}
                    size="md"
                    className={`bg-ctx-interactive-secondary ${filterButtonClass}`}
                  />
                }
              >
                <DropdownMenu
                  onSubmit={handleSubmit(onSubmit)}
                  clearFilters={handleClearFilters}
                />
              </Dropdown>
            </div>
          </div>
        </div>
      </div>
    </FormProvider>
  )
}
