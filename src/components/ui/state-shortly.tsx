'use client'

import { But<PERSON>, IconShape } from '@ads/components-react'
import { useRouter } from 'next/navigation'
import { HiArrowLeft } from 'react-icons/hi2'
import { LuConstruction } from 'react-icons/lu'

export function StateShortly() {
  const router = useRouter()

  return (
    <div className="mx-auto my-10 flex w-full max-w-[450px] flex-col items-center justify-center gap-4">
      <IconShape
        className="bg-ctx-highlight-promote"
        type="brand"
        size="lg"
        icon={() => <LuConstruction size={24} className="text-base-gray-900" />}
      />
      <div>
        <h4 className="text-center text-ctx-content-title ts-heading-xs">
          Estamos trabalhando para trazer algo incrível!
        </h4>
        <p className="mt-1 text-center text-ctx-content-base ts-paragraph-xxs">
          Esta página está em construção. <br />
          Em breve, novidades por aqui.
        </p>
      </div>

      <Button
        hierarchy="primary"
        size="md"
        leadingIcon={HiArrowLeft}
        className="mt-4"
        onClick={() => {
          router.push('/dashboard')
        }}
      >
        Voltar para o início
      </Button>
    </div>
  )
}
