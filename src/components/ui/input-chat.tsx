'use client'

import { I<PERSON><PERSON><PERSON><PERSON>, Tooltip } from '@ads/components-react'
import { type ComponentProps } from 'react'
import { Lu<PERSON>aperclip } from 'react-icons/lu'
import { VscSend } from 'react-icons/vsc'

interface InputChatProps extends ComponentProps<'textarea'> {
  disabled?: boolean
  disabledButtonMessage?: boolean
  onClickSendMessage?: () => void
  placeholder?: string
}

export function InputChat({
  disabled = false,
  disabledButtonMessage = false,
  placeholder = 'Descreva sua dúvida. Aguarde até que será avaliada.',
  onClickSendMessage,
  ...props
}: InputChatProps) {
  function handleClick(e: React.MouseEvent<HTMLElement>, disabled: boolean) {
    if (disabled) return

    const target = e.target as Element

    const isButton =
      target.closest('button') || target.closest('[role="button"]')

    if (!isButton) {
      const textarea = e.currentTarget.querySelector(
        'textarea'
      ) as HTMLTextAreaElement | null
      if (textarea) {
        textarea.focus()
      }
    }
  }

  function handleAutoResize(
    e: React.FormEvent<HTMLTextAreaElement>,
    disabled: boolean
  ) {
    if (disabled) return

    const textarea = e.currentTarget
    textarea.style.height = 'auto'
    textarea.style.height = `${Math.min(textarea.scrollHeight, 8 * 16)}px`
  }

  return (
    <div className="relative w-full">
      <div
        className={`flex cursor-text flex-col items-center rounded-lg border border-ctx-interactive-secondary bg-ctx-interactive-secondary px-2 pt-4 transition-all duration-300 ease-in-out focus-within:border-ctx-highlight-focus focus-within:shadow-[0_0_0_4px_#FFF8D4] ${
          disabled
            ? 'cursor-not-allowed opacity-50'
            : 'hover:bg-ctx-interactive-secondaryHover'
        }`}
        onClick={(e) => {
          handleClick(e, disabled)
        }}
      >
        <div className="flex w-full items-end">
          <textarea
            placeholder={placeholder}
            className="at-overall at-scrollbar at-resize-none at-p-nano at-pl-mini at-align-middle -at-outline-offset-1 at-transition-all at-ts-paragraph-xxs placeholder:at-text-ctx-content-placeholder ${disabled ? 'cursor-not-allowed opacity-50' : ''} h-16 max-h-32 min-h-16 w-full overflow-y-auto bg-transparent pb-0 pt-0 focus:outline-0"
            minLength={3}
            disabled={disabled}
            onInput={(e) => {
              handleAutoResize(e, disabled)
            }}
            {...props}
          />
        </div>
        <div
          className={`bg-primary-surface-primary z-2 flex w-full items-center p-4 pt-0 ${disabled ? 'cursor-not-allowed opacity-50' : ''}`}
        >
          <div className="w-full">
            <div className="flex w-full items-center justify-between">
              <Tooltip title="Em Breve">
                <div className="flex items-center gap-2">
                  <IconButton
                    size="sm"
                    hierarchy="tertiary"
                    icon={() => <LuPaperclip />}
                    ariaLabel="Anexar arquivo"
                    disabled
                  />
                </div>
              </Tooltip>
              <div className="flex items-center gap-2">
                <IconButton
                  size="sm"
                  hierarchy="primary"
                  icon={() => <VscSend />}
                  ariaLabel="Enviar mensagem"
                  disabled={disabledButtonMessage || disabled}
                  onClick={onClickSendMessage}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
