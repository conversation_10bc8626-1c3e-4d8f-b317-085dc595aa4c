import { Checkbox } from '@ads/components-react'

import { cn } from '@/lib/utils'
import { LabelAndValue } from '@/model/labelAndValue'

interface ListItemProps {
  index: number
  style: React.CSSProperties
  data: {
    items: LabelAndValue[]
    selectedValues: Set<string>
    onSelect: (option: LabelAndValue) => void
  }
}

export function ListItem({ index, style, data }: ListItemProps) {
  const { items, selectedValues, onSelect } = data
  const item = items[index]

  if (!item) return null

  const isSelected = selectedValues.has(item.value)

  return (
    <div
      style={style}
      className={cn(
        'text-paragraph-xxs flex cursor-pointer items-center rounded-sm px-2 transition-colors hover:bg-ctx-layout-surface',
        isSelected && 'bg-ctx-layout-body'
      )}
    >
      <Checkbox
        key={item.label}
        className="h-full w-full"
        checked={isSelected}
        custom={{ item: 'bg-ctx-interactive-secondary' }}
        onCheckedChange={() => onSelect(item)}
        label={item.label}
      />
    </div>
  )
}
