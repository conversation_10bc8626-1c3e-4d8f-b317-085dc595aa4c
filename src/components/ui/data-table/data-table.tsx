'use client'
import { Pagination } from '@ads/components-react'
import { Loader2 } from 'lucide-react'

import { cn } from '@/lib/utils'

import { TableSkeleton } from '../../loaders/skeletons/table'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../table'
import { EmptyState } from './empty-state'
import {
  BaseTableRow,
  DataTableProps,
  PaginationInfo,
  TableColumn,
} from './types'
import { getCellValue } from './utils'

export type { DataTableProps, PaginationInfo, TableColumn }

export function DataTable<T extends BaseTableRow>({
  data,
  columns,
  pagination,
  onPageChange,
  loading = false,
  className,
  tableClassName,
  emptyMessage,
  showPagination = true,
  isFetchingNewPage,
}: DataTableProps<T>) {
  return (
    <div
      className={cn(
        'w-full rounded-xl bg-ctx-layout-body p-6 shadow-md',
        className
      )}
    >
      <div className="overflow-x-auto rounded-xl border">
        <Table className={tableClassName}>
          <TableHeader className="text-ctx-content-title ts-paragraph-xxs">
            <TableRow className="rounded-lg">
              {columns.map((column, index) => (
                <TableHead
                  key={column.key}
                  className={cn(
                    'bg-base-gray-100 px-6 dark:bg-base-gray-700',
                    index === 0 && 'rounded-tl-lg',
                    index === columns.length - 1 && 'rounded-tr-lg',
                    column.headerClassName
                  )}
                >
                  {column.header}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableSkeleton columns={columns as []} />
            ) : data.length === 0 ? (
              <EmptyState
                message={emptyMessage ?? 'Nenhum dado encontrado.'}
                colSpan={columns.length}
              />
            ) : (
              data.map((row, index) => (
                <TableRow
                  key={row.id}
                  className={`text-ctx-content-base ts-subtitle-xxs ${index % 2 === 0 ? 'bg-ctx-layout-body' : 'bg-base-gray-100 dark:bg-base-gray-700'} h-20`}
                >
                  {columns.map((column) => (
                    <TableCell
                      key={column.key}
                      className={cn('px-6', column.className)}
                    >
                      {getCellValue(row, column)}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
        {showPagination && !loading && pagination.totalPages > 0 && (
          <div className="flex w-full justify-center py-4">
            <Pagination
              controlText={{ previous: 'Anterior', next: 'Próximo' }}
              activeIndex={pagination.currentPage - 1}
              setActiveIndex={(e) => onPageChange((e as number) + 1)}
              pageAmount={pagination.totalPages}
              ellipsisLabel="Buscar mais itens"
            />
            {isFetchingNewPage && (
              <div className="flex items-center justify-center">
                <Loader2 className="h-4 w-4 animate-spin text-base-primary-500" />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
