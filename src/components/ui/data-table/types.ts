/**
 * Configuration for a table column
 */
export interface TableColumn<T = Record<string, unknown>> {
  /** Unique key for the column */
  key: string
  /** Header text to display */
  header: string
  /** How to access/render the data for this column */
  accessor?: keyof T | ((row: T) => React.ReactNode)
  /** CSS classes for the cell */
  className?: string
  /** CSS classes for the header cell */
  headerClassName?: string
  /** Whether this column is sortable (future feature) */
  sortable?: boolean
}

/**
 * Pagination information and state
 */
export interface PaginationInfo {
  /** Current active page (1-based) */
  currentPage: number
  /** Total number of pages available */
  totalPages: number
  /** Number of items per page */
  itemsPerPage: number
  /** Total number of items across all pages */
  totalItems: number
}

/**
 * Props for the DataTable component
 */
export interface DataTableProps<T = Record<string, unknown>> {
  /** Array of data to display in the table */
  data: T[]
  /** Column configuration */
  columns: TableColumn<T>[]
  /** Pagination state and info */
  pagination: PaginationInfo
  /** Callback when a specific page is selected */
  onPageChange: (page: number) => void
  /** Whether the table is in loading state */
  loading?: boolean
  /** Message to show when no data is available */
  emptyMessage?: string
  /** CSS classes for the container */
  className?: string
  /** CSS classes for the table element */
  tableClassName?: string
  /** Whether to show pagination controls */
  showPagination?: boolean
  /** Whether a new page is being fetched */
  isFetchingNewPage?: boolean
}

/**
 * Common table row data interface
 * Extend this for your specific data types
 */
export interface BaseTableRow {
  id: string | number
  [key: string]: unknown
}

/**
 * Sort configuration (for future sorting feature)
 */
export interface SortConfig {
  key: string
  direction: 'asc' | 'desc'
}

/**
 * Filter configuration (for future filtering feature)
 */
export interface FilterConfig {
  key: string
  value: unknown
  operator:
    | 'equals'
    | 'contains'
    | 'startsWith'
    | 'endsWith'
    | 'greaterThan'
    | 'lessThan'
}

/**
 * Advanced table configuration (for future features)
 */
export interface AdvancedTableConfig {
  /** Enable row selection */
  selectable?: boolean
  /** Enable sorting */
  sortable?: boolean
  /** Enable filtering */
  filterable?: boolean
  /** Enable column resizing */
  resizable?: boolean
  /** Enable column reordering */
  reorderable?: boolean
}
