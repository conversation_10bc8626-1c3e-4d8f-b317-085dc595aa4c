'use client'

import { IconShape } from '@ads/components-react'
import { ComponentType } from 'react'
import { HiOutlineSquares2X2 } from 'react-icons/hi2'

import { cn } from '@/lib/utils'

import { Card } from '../card'

export interface ShortcutCardProps
  extends React.HTMLAttributes<HTMLDivElement> {
  disabled?: boolean
  type?: 'default' | 'shortly'
  onClick?: () => void
  data: {
    title: string
    description: string
    Icon?: ComponentType
  }
}

export function ShortcutCard({
  className,
  type = 'default',
  disabled = type === 'shortly',
  data: { title, description, Icon = HiOutlineSquares2X2 },
  onClick = () => {},
  ...props
}: ShortcutCardProps) {
  const isShortly = type === 'shortly'

  return (
    <div className={cn('w-fit', isShortly && 'relative block')}>
      {isShortly && (
        <span className="py absolute right-3 top-3 rounded-pill bg-base-brand-600 px-3 text-ctx-content-titleAlternative ts-paragraph-xxxs">
          Em breve
        </span>
      )}
      <Card
        className={cn(
          'h-44 w-56 cursor-pointer rounded-2xl border border-ctx-interactive-secondary bg-ctx-interactive-secondary p-4 shadow-lg hover:bg-ctx-interactive-secondaryHover focus:shadow-[0_0_0_4px_#FFF8D4] focus:outline-none',
          disabled && 'pointer-events-none select-none opacity-50',
          className
        )}
        tabIndex={0}
        onClick={disabled ? undefined : onClick}
        onKeyDown={(event) => {
          if (event.key === 'Enter' && !disabled) {
            onClick()
          }
        }}
        {...props}
      >
        {isShortly && (
          <span className="py absolute right-3 top-3 rounded-pill bg-base-brand-600 px-3 text-ctx-content-titleAlternative ts-paragraph-xxxs">
            Em breve
          </span>
        )}
        <div>
          <div className="mb-4 flex flex-col gap-nano">
            <IconShape
              icon={Icon}
              type="brand"
              size="sm"
              className="light:bg-ctx-interactive-secondary"
            />
            <h3 className="text-ctx-content-title ts-subtitle-xxxs">{title}</h3>
          </div>
          <p className="line-clamp-3 text-ctx-content-base ts-paragraph-xxxs">
            {description}
          </p>
        </div>
      </Card>
    </div>
  )
}
