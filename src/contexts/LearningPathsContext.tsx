'use client'

import { createContext, ReactNode, useContext, useRef, useState } from 'react'
import { ImperativePanelHandle } from 'react-resizable-panels'

import { useChatStore } from '@/store/useChatStore'

interface LearningPathsContextType {
  isExpanded: boolean
  isClosed: boolean
  setIsExpanded: (value: boolean) => void
  setIsClosed: (value: boolean) => void
  leftPanelRef: React.RefObject<ImperativePanelHandle | null>
  handleExpandToggle: () => void
  handleLeftPanelCollapse: () => void
  handleLeftPanelExpand: () => void
  handleClose: () => void
  handleCreateWithAI: () => void
  clearChatId: () => void
}

const LearningPathsContext = createContext<
  LearningPathsContextType | undefined
>(undefined)

export function LearningPathsProvider({ children }: { children: ReactNode }) {
  const { setChatId } = useChatStore()

  const [isExpanded, setIsExpanded] = useState(false)
  const [isClosed, setIsClosed] = useState(true)

  const leftPanelRef = useRef<ImperativePanelHandle>(null)

  function handleExpandToggle() {
    if (!leftPanelRef.current) return

    if (isExpanded) {
      setIsExpanded(false)
      leftPanelRef.current?.expand()
      leftPanelRef.current?.resize(75)
      return
    }

    setIsExpanded(true)
    setTimeout(() => {
      leftPanelRef.current?.collapse()
    }, 0)
  }

  function handleLeftPanelCollapse() {
    setIsExpanded(true)
  }

  function handleLeftPanelExpand() {
    setIsExpanded(false)
  }

  function clearChatId() {
    setChatId(null)
  }

  function handleClose() {
    setIsClosed(true)
    clearChatId()
  }

  function handleCreateWithAI() {
    setIsClosed(false)
    setIsExpanded(true)
    clearChatId()
  }

  return (
    <LearningPathsContext.Provider
      value={{
        isExpanded,
        isClosed,
        setIsExpanded,
        setIsClosed,
        leftPanelRef,
        handleExpandToggle,
        handleLeftPanelCollapse,
        handleLeftPanelExpand,
        handleClose,
        handleCreateWithAI,
        clearChatId,
      }}
    >
      {children}
    </LearningPathsContext.Provider>
  )
}

export function useLearningPaths() {
  const context = useContext(LearningPathsContext)

  if (context === undefined) {
    throw new Error(
      'useLearningPaths must be used within a LearningPathsProvider'
    )
  }

  return context
}
