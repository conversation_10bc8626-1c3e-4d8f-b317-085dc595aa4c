import {
  CreateStudyPlanMutation,
  CreateStudyPlanMutationVariables,
  CreateStudyPlanV2MutationVariables,
  GetStudyPlansQuery,
  GetStudyPlansQueryVariables,
  StudyPlanQuery,
  type UpdatedTeamsAndUserPlanStudyMutation,
  type UpdatedTeamsAndUserPlanStudyMutationVariables,
  UpdateStudyPlanV2Mutation,
  UpdateStudyPlanV2MutationVariables,
} from '@/graphql/generated/graphql'
import {
  CREATE_STUDY_PLAN,
  CREATE_STUDY_PLAN_V2,
  DELETE_STUDY_PLAN,
  UPDATE_STUDY_PLAN_V2,
  UPDATE_TEAMS_AND_USER_STUDY_PLAN,
} from '@/graphql/study-plan/mutations'
import {
  GET_SPECIFIC_STUDY_PLAN,
  GET_STUDY_PLANS,
} from '@/graphql/study-plan/queries'
import { clientGraphql } from '@/services/graphql'

import { CreateStudyPlanV2Mutation } from '../graphql/generated/graphql'

export const getStudyPlans = (data: GetStudyPlansQueryVariables) =>
  clientGraphql.request<GetStudyPlansQuery>(
    GET_STUDY_PLANS,
    {
      ...data,
    },
    {
      'cache-control': 'no-cache',
    }
  )

export const createNewStudyPlan = (data: CreateStudyPlanMutationVariables) =>
  clientGraphql.request<CreateStudyPlanMutation>(CREATE_STUDY_PLAN, {
    ...data,
  })

export const createNewStudyPlanV2 = (
  data: CreateStudyPlanV2MutationVariables
) =>
  clientGraphql.request<CreateStudyPlanV2Mutation>(CREATE_STUDY_PLAN_V2, {
    ...data,
  })

export const updateStudyPlanV2 = (data: UpdateStudyPlanV2MutationVariables) =>
  clientGraphql.request<UpdateStudyPlanV2Mutation>(UPDATE_STUDY_PLAN_V2, {
    ...data,
  })

export const getSpecificStudyPlan = (id: number) =>
  clientGraphql.request<StudyPlanQuery>(
    GET_SPECIFIC_STUDY_PLAN,
    {
      id,
    },
    {
      'cache-control': 'no-cache',
    }
  )

export const deleteStudyPlan = (id: number) =>
  clientGraphql.request(DELETE_STUDY_PLAN, {
    id,
  })

export const updateTeamsUsersStudyPlan = (
  data: UpdatedTeamsAndUserPlanStudyMutationVariables
) =>
  clientGraphql.request<UpdatedTeamsAndUserPlanStudyMutation>(
    UPDATE_TEAMS_AND_USER_STUDY_PLAN,
    {
      ...data,
    }
  )
