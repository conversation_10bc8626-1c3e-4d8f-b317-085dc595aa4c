import { GET_DASHBOARD_ENGAGEMENT_DETAILS } from '@/graphql/dashboard/queries'
import {
  GetDashboardEngagementDetailsQuery,
  GetDashboardEngagementDetailsQueryVariables,
} from '@/graphql/generated/graphql'
import { clientGraphql } from '@/services/graphql'

export const getEngagementDetails = (
  data: GetDashboardEngagementDetailsQueryVariables
) =>
  clientGraphql.request<GetDashboardEngagementDetailsQuery>(
    GET_DASHBOARD_ENGAGEMENT_DETAILS,
    { ...data },
    {
      'cache-control': 'with-cache',
    }
  )
