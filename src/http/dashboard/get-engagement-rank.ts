import { GET_DASHBOARD_ENGAGEMENT_RANK } from '@/graphql/dashboard/queries'
import {
  GetDashboardEngagementRankQuery,
  GetDashboardEngagementRankQueryVariables,
} from '@/graphql/generated/graphql'
import { clientGraphql } from '@/services/graphql'

export const getEngagementRank = (
  data: GetDashboardEngagementRankQueryVariables
) =>
  clientGraphql.request<GetDashboardEngagementRankQuery>(
    GET_DASHBOARD_ENGAGEMENT_RANK,
    { ...data },
    {
      'cache-control': 'with-cache',
    }
  )
