import { Avatar } from '@/model/avatar'
import api from '@/services/api'

type User = {
  name: string
  phone_number: string
  roles: {
    id: number
    name: string
    slug: string
  }[]
  id: number
  avatar: Avatar | null
  metadata: {
    company_id: number | null
  }
}

type ResponseSession = {
  token: string
  user: User
}

interface getSessioAdminProps {
  email: string
  password: string
}

export async function getSessioAdmin({ email, password }: getSessioAdminProps) {
  const {
    data: { token, user },
  } = await api.post<ResponseSession>('sessions', { email, password })

  return { token, user }
}
