import { ESolutionContext } from '@/enum/solution-context'
import { GET_COURSES } from '@/graphql/courses/queries'
import { GetCoursesQuery } from '@/graphql/generated/graphql'
import { clientGraphql } from '@/services/graphql'

export type getCoursesRequest = {
  page: number
  all: boolean
  limit: number
  q?: string
  available_at: ESolutionContext
}

export const getCourses = ({
  page,
  all,
  limit,
  q,
  available_at,
}: getCoursesRequest) =>
  clientGraphql.request<GetCoursesQuery>(GET_COURSES, {
    page,
    all,
    limit,
    q,
    available_at,
  })
