import { GET_ENROLLMENTS_B2B_BY_COMPANY_ID } from '@/graphql/enrollments/queries'
import {
  GetB2BUsersQuery,
  GetEnrollmentsByCompanyIdQuery,
} from '@/graphql/generated/graphql'
import { GET_B2B_USERS } from '@/graphql/teams/queries'
import { clientGraphql } from '@/services/graphql'

export type UsersTeamsType = {
  id: number
  name: string
  email: string
  metadata: {
    squad_id?: number | null
    company_squad: {
      title: string
    } | null
  } | null
}

export type GetUserProps = {
  id?: number
  q?: string
  orderBy?: string
  order?: string
  page?: number
  company_id?: number
  enrollment_id?: number
  limit?: number
  hasSquad?: boolean | null
}

export const getEnrollmentsByCompanyIdUsers = (criteria: string) =>
  clientGraphql.request<GetEnrollmentsByCompanyIdQuery>(
    GET_ENROLLMENTS_B2B_BY_COMPANY_ID,
    {
      criteria,
    }
  )

export const getB2bUsers = ({
  page = 1,
  orderBy,
  company_id,
  enrollment_id,
  order = 'ASC',
  q,
  limit = 8,
  hasSquad = null,
}: GetUserProps) =>
  clientGraphql.request<GetB2BUsersQuery>(
    GET_B2B_USERS,
    {
      all: false,
      limit: limit,
      order: order,
      page,
      orderBy,
      company_id,
      enrollment_id,
      q,
      hasSquad,
    },
    {
      'cache-control': 'no-cache',
    }
  )
