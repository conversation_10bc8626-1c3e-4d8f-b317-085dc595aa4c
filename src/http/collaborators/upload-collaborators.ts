import api from '@/services/api'

export enum ErrorCodeUploadColLaborators {
  'CMP-0003' = 'Lincença não informada',
  'CMP-0004' = 'Um ou mais emails estão incorretos',
  'INVLD-EMLS' = 'Um ou mais emails são inválidos',
}

export const uploadColaborators = async (file: File, enrollmentId: string) => {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('enrollmentId', String(enrollmentId))

  return api.post('/companies/batch-upload-users', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}
