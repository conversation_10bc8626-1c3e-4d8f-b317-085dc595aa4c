import { GET_COLLABORATORS } from '@/graphql/collaborators/queries'
import {
  GetCollaboratorsQuery,
  GetCollaboratorsQueryVariables,
} from '@/graphql/generated/graphql'
import { clientGraphql } from '@/services/graphql'

export const getCollaborators = (data: GetCollaboratorsQueryVariables) =>
  clientGraphql.request<GetCollaboratorsQuery>(
    GET_COLLABORATORS,
    {
      ...data,
    },
    {
      'cache-control': 'no-cache',
    }
  )

export const getCollaboratorsWithCache = (
  data: GetCollaboratorsQueryVariables
) =>
  clientGraphql.request<GetCollaboratorsQuery>(GET_COLLABORATORS, {
    ...data,
  })
