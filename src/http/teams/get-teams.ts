import { GetTeamsQuery } from '@/graphql/generated/graphql'
import { GET_TEAMS } from '@/graphql/teams/queries'
import { clientGraphql } from '@/services/graphql'

type GetTeamsProps = {
  limit?: number
  page?: number
  company_id?: number
  title?: string
}

export const getTeams = (data: GetTeamsProps) =>
  clientGraphql.request<GetTeamsQuery>(
    GET_TEAMS,
    {
      ...data,
    },
    {
      'cache-control': 'no-cache',
    }
  )
