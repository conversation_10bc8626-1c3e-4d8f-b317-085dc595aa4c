import axios from 'axios'
import Cookies from 'universal-cookie'

const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  headers: {
    'Cache-Control': 'no-cache',
  },
})

api.interceptors.request.use((config) => {
  const cookies = new Cookies(null, { path: '/' })
  const b2bAuthToken = cookies.get('b2bAuthToken')

  if (b2bAuthToken) {
    config.headers.Authorization = `Bearer ${b2bAuthToken}`
  }

  return config
})

export default api
