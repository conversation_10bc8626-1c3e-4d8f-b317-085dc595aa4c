import { EPosition } from '@/enum/position'
import { ESeniority } from '@/enum/seniority'

import { Avatar } from './avatar'
import { IRole } from './roles'

export interface IUser {
  address: string
  address_id: string
  admitted_at?: string
  avatar?: Avatar
  avatar_id: string
  birthdate: string
  blocked_at: string
  cpf: string
  created_at: string
  email: string
  email_score: string
  foreign_document: string
  gender: string
  hidden_at: string
  id: number
  is_blocked: boolean
  is_hidden: boolean
  last_login: string
  lms_user_id: string
  groups?: string[]
  metadata: {
    user_id: number
    has_forced_password_change: boolean
    b2b_confirmation_account_token: string
    company_id: number
    squad_id: number
  }
  name: string
  nationality_id: string
  occupation: string
  permissions: number[]
  phone_number: string
  position: EPosition
  roles: IRole[]
  seniority: ESeniority
  status: boolean
  updated_at: string
}
