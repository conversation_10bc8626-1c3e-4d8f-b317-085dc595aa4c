export type StudyPlanStatus = 'ACTIVE' | 'INACTIVE' | 'FINISHED' | 'ALL'

export type StudyPlanData = {
  name: string
  coursesCount: number
  squadsCount: number
  usersCount: number
  id: number
  ai_generated: boolean | null
  end_date: Date | null
  status: boolean
  users_completed_count: number | null
  courses_pivot:
    | {
        course_id: number
        course: { id: string; title: string } | null
      }[]
    | null
}

interface Category {
  title: string
}

export type CoursesData = {
  id: string | number
  title: string
  categories: Category[]
}

export type CollaboratorData = {
  id: string | number
  name: string
  email: string
  metadata?: {
    company_squad: {
      title: string
    }
  }
}

export type SquadsData = {
  id: string | number
  title: string
}

export type GroupsData = {
  id: string | number
  name: string
}
